from dotenv import load_dotenv
import os
from passlib.context import Crypt<PERSON>ontext
from datetime import date, time, datetime, timedelta
import jwt
from pydantic import BaseModel, Field # Added Field for default values
from typing import Optional, List, Literal, Tuple # Added Literal for rate_type validation
import json
import uuid # Add this import at the top of your file
import shutil # Add this import at the top of your file
from decimal import Decimal

from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Depends, HTTPException, status
from passlib.exc import UnknownHashError

# SQLAlchemy imports
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from models import User, Client, TutoringJob, Attendance, Invoice, Payslip
from database import get_db_session, init_models, close_db

load_dotenv()

SECRET_KEY = os.environ.get("SECRET_KEY")
DATABASE_URL = os.environ.get("DATABASE_URL")
UPLOAD_PROOF_DIR = "uploads/proofs" 

app = FastAPI()

# Define allowed origins
origins = ["*"] # Allow requests from any origin
# Apply CORS settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Restrict to specific origins
    allow_credentials=True,
    allow_methods=["*"],  # Allowed HTTP methods
    allow_headers=["*"],  # Allowed headers
)

# --- Pydantic Models ---
# Pydantic model for creating a new client
class ClientCreate(BaseModel):
    student_name: str
    student_whatsapp: Optional[str] = None
    pj_name: Optional[str] = None
    email: Optional[str] = None
    pj_whatsapp: Optional[str] = None
    school: Optional[str] = None
    grade: Optional[str] = None

# Pydantic model for updating an existing client (all fields optional)
class ClientUpdate(BaseModel):
    student_name: Optional[str] = None
    student_whatsapp: Optional[str] = None
    pj_name: Optional[str] = None
    email: Optional[str] = None
    pj_whatsapp: Optional[str] = None
    school: Optional[str] = None
    grade: Optional[str] = None

# Pydantic model for retrieving client data
class Client(BaseModel):
    client_id: int
    student_name: str
    student_whatsapp: Optional[str] = None
    pj_name: Optional[str] = None
    email: Optional[str] = None
    pj_whatsapp: Optional[str] = None
    school: Optional[str] = None
    grade: Optional[str] = None
    created_at: datetime
    deleted_at: Optional[datetime] = None

# Pydantic model for creating a new tutoring job
class JobCreate(BaseModel):
    program_title: str
    mentor_ids: List[int]
    client_ids: List[int]
    client_rate: float = Field(default=0.0) # Use float, default 0
    mentor_rate: float = Field(default=0.0) # Use float, default 0
    rate_type: Literal["per hour", "per session/program"] # Enforce specific values

# Pydantic model for updating an existing tutoring job (all fields optional)
class JobUpdate(BaseModel):
    program_title: Optional[str] = None
    mentor_ids: Optional[List[int]] = None
    client_ids: Optional[List[int]] = None
    client_rate: Optional[float] = None
    mentor_rate: Optional[float] = None
    rate_type: Optional[Literal["per hour", "per session/program"]] = None

# Pydantic model for retrieving tutoring job data
class Job(BaseModel):
    job_id: int
    program_title: str
    mentor_ids: List[int]
    client_ids: List[int]
    client_rate: float # Use float for NUMERIC
    mentor_rate: float # Use float for NUMERIC
    rate_type: Optional[str] = None
    created_at: datetime
    deleted_at: Optional[datetime] = None

# Pydantic model for retrieving client data and my jobs details
class ClientBasicInfo(BaseModel):
    student_name: str
    student_whatsapp: Optional[str] = None
    school: Optional[str] = None
    grade: Optional[str] = None
    # Add client_id if you want to identify them easily, though not explicitly requested for display
    # client_id: int 

class MyJobDetail(BaseModel):
    job_id: int
    program_title: str
    mentor_ids: List[int]
    # client_ids: List[int] # You can choose to keep or remove this. client_details provides the info.
    mentor_rate: float # mentor_rate is kept
    rate_type: Optional[str] = None # Allow rate_type to be optional
    created_at: datetime
    deleted_at: Optional[datetime] = None
    clients: List[ClientBasicInfo] # List of client details

# --- Pydantic Model: AttendanceCreate ---
class AttendanceCreate(BaseModel):
    session_date: date
    start_time: time
    end_time: time
    duration: str # e.g., "2 hours", "90 minutes"
    activity_log: Optional[str] = None
    photo_proof_url: Optional[str] = None # This will be the filename returned by /uploadproof

class AttendanceRecord(BaseModel):
    record_id: int
    job_id: int
    session_date: date
    start_time: time
    end_time: time
    duration: Optional[str] = None
    activity_log: Optional[str] = None
    photo_proof_url: Optional[str] = None
    tutor_id: int
    tutor_name: str
    client_ids: List[int]
    student_names: List[str]
    program_title: str
    status: str
    mentor_rate: Optional[float] = None # Use float for NUMERIC
    client_rate: Optional[float] = None # Use float for NUMERIC
    rate_type: Optional[str] = None
    submitted_at: datetime
    deleted_at: Optional[datetime] = None
    invoice_ids: Optional[List[int]] = None # To store multiple invoice IDs
    payslip_id: Optional[int] = None

# Pydantic Model for My Attendance Record
class MyAttendanceRecord(BaseModel): # New model without client_rate
    record_id: int
    job_id: int
    session_date: date
    start_time: time
    end_time: time
    duration: Optional[str] = None
    activity_log: Optional[str] = None
    photo_proof_url: Optional[str] = None
    tutor_id: int
    tutor_name: str # This is already in the attendance table
    client_ids: List[int]
    student_names: List[str]
    program_title: str
    status: str
    mentor_rate: Optional[float] = None # Use float for NUMERIC
    # client_rate is intentionally omitted
    rate_type: Optional[str] = None
    submitted_at: datetime
    deleted_at: Optional[datetime] = None
    invoice_ids: Optional[List[int]] = None # To store multiple invoice IDs
    payslip_id: Optional[int] = None

class AttendanceUpdate(BaseModel):
    session_date: Optional[date] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    duration: Optional[str] = None
    activity_log: Optional[str] = None

class AdminAttendanceRecord(BaseModel):
    record_id: int
    job_id: int
    session_date: date
    start_time: time
    end_time: time
    duration: Optional[str] = None
    activity_log: Optional[str] = None # Not explicitly in your list, but usually useful. Add if needed.
    # photo_proof_url: Optional[str] = None # Also not in list, add if needed.
    tutor_id: int # Added for completeness, though "Tutor" (name) is requested
    tutor_name: str # "Tutor"
    client_ids: List[int] # Added for completeness, though "Student" (names) are requested
    student_names: List[str] # "Student"
    program_title: str # "Program"
    status: str # "Status" (first one)
    mentor_rate: Optional[float] = None
    client_rate: Optional[float] = None # Included as requested
    rate_type: Optional[str] = None
    submitted_at: datetime
    deleted_at: Optional[datetime] = None # Good to have for admin view
    invoice_ids: Optional[List[int]] = None # To store multiple invoice IDs
    payslip_id: Optional[int] = None

class AttendanceStatusUpdateResponse(BaseModel):
    record_id: int
    status: str

class AdminAttendanceUpdate(BaseModel):
    session_date: Optional[date] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    client_rate: Optional[float] = None # NUMERIC in DB, float in Pydantic
    mentor_rate: Optional[float] = None # NUMERIC in DB, float in Pydantic
    status: Optional[Literal["pending", "approved", "rejected"]] = None

# --- Pydantic Models for Invoice and Payslip ---

class AdjustmentItem(BaseModel):
    description: str
    amount: float # Positive for cost/addition, negative for reduction/deduction

class InvoiceLineItem(BaseModel):
    attendance_record_id: int
    description: str
    quantity: float # e.g., 1 for session, or hours
    unit_price: float
    total_price: float

class ClientSnapshot(BaseModel):
    client_id: int # Store the ID for reference
    student_name: str
    student_whatsapp: Optional[str] = None
    pj_name: Optional[str] = None
    email: Optional[str] = None
    pj_whatsapp: Optional[str] = None
    school: Optional[str] = None
    grade: Optional[str] = None

class InvoiceBase(BaseModel):
    due_date: Optional[date] = None
    notes: Optional[str] = None
    adjustments: List[AdjustmentItem] = []

class InvoiceCreateRequest(BaseModel):
    attendance_record_ids: List[int]
    cutoff_date: date
    due_date: Optional[date] = None

class Invoice(InvoiceBase):
    invoice_id: int
    client_id: int
    invoice_number: str
    issue_date: date
    status: Literal['checking', 'verified', 'sent', 'paid', 'cancelled']
    client_details: ClientSnapshot
    line_items: List[InvoiceLineItem]
    subtotal: float
    total_amount: float
    generated_by_user_id: int
    verified_by_user_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    payment_details: Optional[dict] = None # e.g., {"payment_date": "YYYY-MM-DD", "method": "transfer", "notes": "..."}

class InvoiceAdminUpdate(BaseModel): # For admin verification step
    due_date: Optional[date] = None
    notes: Optional[str] = None
    adjustments: Optional[List[AdjustmentItem]] = None

class InvoiceStatusUpdateRequest(BaseModel):
    status: Literal['verified', 'sent', 'paid', 'cancelled']
    payment_details: Optional[dict] = None # Required if status is 'paid'
    notes: Optional[str] = None # e.g. cancellation reason

# --- Payslip Models ---

class PayslipEarningItem(BaseModel):
    attendance_record_id: int
    description: str
    quantity: float # e.g., 1 for session, or hours
    rate: float
    total_earned: float

class TutorSnapshot(BaseModel):
    user_id: int # Store the ID for reference
    name: str
    email: str
    whatsapp: Optional[str] = None
    # expertise: Optional[str] = None # Add if needed in payslip snapshot

class PayslipBase(BaseModel):
    notes: Optional[str] = None
    adjustments: List[AdjustmentItem] = []

class PayslipCreateRequest(BaseModel):
    # Option 1: Explicitly provide record IDs
    # attendance_record_ids: List[int]
    # Option 2: Auto-detect based on period (more aligned with request)
    pay_period_start_date: date
    pay_period_end_date: date
    # tutor_id: Optional[int] = None # To generate for a specific tutor, or all eligible if None

class Payslip(PayslipBase):
    payslip_id: int
    user_id: int # Tutor's user_id
    payslip_number: str
    pay_period_start_date: date
    pay_period_end_date: date
    issue_date: date
    status: Literal['checking', 'verified', 'paid', 'cancelled']
    tutor_details: TutorSnapshot
    earnings_items: List[PayslipEarningItem]
    subtotal_earnings: float
    total_payout: float
    generated_by_user_id: int
    verified_by_user_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    payment_date: Optional[date] = None

class PayslipAdminUpdate(BaseModel): # For admin verification step
    notes: Optional[str] = None
    adjustments: Optional[List[AdjustmentItem]] = None

class PayslipStatusUpdateRequest(BaseModel):
    status: Literal['verified', 'paid', 'cancelled']
    payment_date: Optional[date] = None # Required if status is 'paid'
    notes: Optional[str] = None # e.g. cancellation reason

# --- Helper function to parse duration string to hours (Example) ---
def parse_duration_to_hours(duration_str: Optional[str]) -> float:
    """
    Parses duration string to hours.
    - If duration_str is a number (e.g. "240"), treat as minutes.
    - If duration_str is in HH:MM:SS, parse as hours.
    - If duration_str contains 'hour' or 'minute', parse accordingly.
    - Returns float hours, rounded to 2 decimals.
    """
    if not duration_str:
        return 0.0

    # Try to parse as integer/float (minutes)
    try:
        minutes = float(duration_str)
        if minutes > 0:
            return round(minutes / 60.0, 2)
    except ValueError:
        pass

    # Try to parse HH:MM:SS
    try:
        if ":" in duration_str:
            parts = duration_str.strip().split(":")
            if len(parts) == 3:
                h, m, s = map(int, parts)
                return round(h + m / 60 + s / 3600, 2)
    except Exception:
        pass

    # Fallback: parse text like '2 hours', '90 minutes'
    duration_str = duration_str.lower()
    total_hours = 0.0
    hours = 0.0
    minutes = 0.0

    if "hour" in duration_str:
        parts = duration_str.split("hour")
        try:
            hours = float(parts[0].strip())
            duration_str = parts[1] if len(parts) > 1 else ""
        except ValueError:
            pass

    if "minute" in duration_str:
        parts = duration_str.split("minute")
        try:
            minutes = float(parts[0].replace("s", "").strip())
        except ValueError:
            pass

    total_hours = hours + (minutes / 60.0)
    if total_hours > 0:
        return round(total_hours, 2)
    return 1.0

# --- Helper function for generating unique numbers ---
async def generate_unique_number(conn: asyncpg.Connection, prefix: str, table_name: str, column_name: str) -> str:
    # This is a simplified example. For robust sequential numbers,
    # you might use a database sequence or a more complex locking mechanism.
    # Using timestamp and a random element for uniqueness here.
    timestamp_part = datetime.utcnow().strftime("%Y%m%d%H%M%S")
    random_part = uuid.uuid4().hex[:6].upper()
    # Ensure it's actually unique in the table
    while True:
        num = f"{prefix}-{timestamp_part}-{random_part}"
        exists = await conn.fetchval(f"SELECT 1 FROM {table_name} WHERE {column_name} = $1", num)
        if not exists:
            return num
        random_part = uuid.uuid4().hex[:6].upper() # Regenerate if collision

# --- Pydantic Model for Eligible Attendance Records ---
class EligibleAttendanceRecordInfo(BaseModel):
    record_id: int
    student_names: List[str] # From attendance.student_names
    program_title: str
    session_date: date
    # Optional: You might want to include client_ids if the frontend needs to know which clients are associated
    # client_ids: List[int]

# Pydantic model for tutor requesting a job
class JobRequestCreate(BaseModel):
    program_name_actual: str = Field(..., description="The actual name of the program/subject.")
    student_names_text: str = Field(..., description="Comma or '&' separated list of student names, e.g., 'Alice, Bob' or 'Alice & Bob'.")
    # Optional: rate_type could be suggested by tutor, but admin finalizes
    # rate_type_suggestion: Optional[Literal["per hour", "per session/program"]] = None 

# Pydantic model for admin finalizing a requested job
class JobRequestedFinalize(BaseModel):
    program_title: str = Field(..., description="The final, clean program title for the job.")
    mentor_ids: List[int]
    client_ids: List[int]
    client_rate: float = Field(default=0.0)
    mentor_rate: float = Field(default=0.0)
    rate_type: Literal["per hour", "per session/program"]

# --- Root Endpoint ---

@app.get("/")
def read_root():
    return {"message": "Hello, world!"}

# --- Security and DB Setup ---

pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

# Use connection pool for better performance
@app.on_event("startup")
async def startup_event():
    try:
        app.state.pool = await asyncpg.create_pool(DATABASE_URL, min_size=1, max_size=10)
        print("Database connection pool created successfully.")
    except Exception as e:
        print(f"Error creating database connection pool: {e}")
        # Optionally raise the error or exit if the pool is critical
        # raise e

@app.on_event("shutdown")
async def shutdown_event():
    if hasattr(app.state, 'pool') and app.state.pool:
        await app.state.pool.close()
        print("Database connection pool closed.")

# Dependency to get a connection from the pool
async def get_db_connection():
    if not hasattr(app.state, 'pool') or not app.state.pool:
         raise HTTPException(status_code=503, detail="Database connection pool not available")
    async with app.state.pool.acquire() as connection:
        yield connection

# --- Auth Helper Functions ---

def hash_password(password: str):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: timedelta):
    to_encode = data.copy()
    expire = datetime.utcnow() + expires_delta
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm="HS256")
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str):
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except UnknownHashError: # Handle cases where the hash format is unknown/invalid
        return False
    except ValueError: # Handle potential errors during verification (e.g., malformed hash)
        return False

# Helper function to parse composite program title
def _parse_requested_job_title(composite_title: str) -> Tuple[Optional[str], Optional[str], bool]:
    """
    Parses a composite title like "Actual Program Name | Student Name(s) | [REQUESTED]"
    or "Actual Program Name | [REQUESTED]" if student names are empty.
    Returns (actual_program_name, student_names_text, is_requested_format)
    """
    is_requested_format = False
    actual_program_name = None
    student_names_text = None

    if composite_title and composite_title.endswith(" | [REQUESTED]"):
        is_requested_format = True
        base_title = composite_title.removesuffix(" | [REQUESTED]")
        parts = base_title.split(" | ", 1)
        if len(parts) == 2:
            actual_program_name = parts[0].strip()
            student_names_text = parts[1].strip()
        elif len(parts) == 1: # Only program name, no student names part
            actual_program_name = parts[0].strip()
            student_names_text = "" # Default to empty string for student names
    elif composite_title and composite_title.endswith("|[REQUESTED]"): # Handle no space before pipe
        is_requested_format = True
        base_title = composite_title.removesuffix("|[REQUESTED]")
        parts = base_title.split("|", 1) # Split by pipe without space
        if len(parts) == 2:
            actual_program_name = parts[0].strip()
            student_names_text = parts[1].strip()
        elif len(parts) == 1:
            actual_program_name = parts[0].strip()
            student_names_text = ""
    
    if not is_requested_format: # If not the special format, return original title as actual
        return composite_title, None, False

    return actual_program_name, student_names_text, is_requested_format

# Helper function to construct composite program title
def _construct_requested_job_title(actual_program_name: str, student_names_text: Optional[str]) -> str:
    if student_names_text and student_names_text.strip():
        return f"{actual_program_name.strip()} | {student_names_text.strip()} | [REQUESTED]"
    return f"{actual_program_name.strip()} | [REQUESTED]"

# --- JWT Authentication Endpoints ---

@app.post("/jwt/login")
async def login_for_access_token(email: str, password: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    user = await conn.fetchrow("SELECT * FROM users WHERE email = $1 AND deleted_at IS NULL", email) # Check for deleted_at
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not verify_password(password, user['hashed_password']):
         raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect email or password", headers={"WWW-Authenticate": "Bearer"})

    user_data = dict(user) # Convert the asyncpg.Record to a dictionary
    del user_data['hashed_password'] # Remove the hashed_password
    del user_data['deleted_at'] # Remove deleted_at if present
    access_token_expires = timedelta(days=30)
    access_token = create_access_token(data={"sub": str(user['user_id']), "role": user['role']}, expires_delta=access_token_expires)
    print("Access token:", access_token)
    return {"access_token": access_token, "token_type": "bearer", "user": user_data}

@app.post("/jwt/renew")
async def renew_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"], options={"verify_exp": False}) # Decode even if expired initially
        user_id: int = payload.get("sub")
        role: str = payload.get("role")
        if user_id is None or role is None:
            raise HTTPException(status_code=401, detail="Invalid token payload")

        # Check expiration manually AFTER decoding to allow renewal
        if datetime.utcnow() > datetime.utcfromtimestamp(payload.get("exp", 0)):
             raise HTTPException(status_code=401, detail="Token has expired") # Or allow renewal for a grace period

        # Optional: Check if user still exists and is active in DB
        # async with app.state.pool.acquire() as conn:
        #     user_exists = await conn.fetchval("SELECT 1 FROM users WHERE user_id = $1 AND deleted_at IS NULL", user_id)
        #     if not user_exists:
        #         raise HTTPException(status_code=401, detail="User associated with token not found or inactive")

        access_token_expires = timedelta(hours=1)
        new_access_token = create_access_token(data={"sub": user_id, "role": role}, expires_delta=access_token_expires)
        return {"access_token": new_access_token, "token_type": "bearer"}
    # except jwt.ExpiredSignatureError: # This won't be caught if verify_exp=False
    #     raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.post("/jwt/check")
async def check_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"]) # Verifies signature and expiration
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token payload")

        # Optional: Add a check if user_id actually exists and is active in DB
        # async with app.state.pool.acquire() as conn:
        #     user_exists = await conn.fetchval("SELECT 1 FROM users WHERE user_id = $1 AND deleted_at IS NULL", user_id)
        #     if not user_exists:
        #         raise HTTPException(status_code=401, detail="User associated with token not found or inactive")

        return {"message": "TOKEN_VALID"}
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.post("/jwt/logout")
async def logout_user(token: str):
    # In a real-world scenario, implement token blocklisting here (e.g., using Redis or a database table).
    # For this example, we just return a success message.
    # Decoding the token first can ensure it was valid before "logging out".
    try:
        jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        # Add token to blocklist here
        return {"message": "Successfully logged out"}
    except jwt.ExpiredSignatureError:
        # Even if expired, maybe still blocklist it or just return success
        return {"message": "Successfully logged out (token was expired)"}
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token provided for logout")


# --- Helper Function for Token Decoding and Role Check ---
async def get_current_user_role(token: str) -> tuple[str, str]:
    """Decodes token, verifies user, returns user_id and role, or raises HTTPException."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        user_id: Optional[str] = payload.get("sub")
        role: Optional[str] = payload.get("role")
        if user_id is None or role is None:
            raise credentials_exception
        # Optional: Check if user exists and is active in DB
        # async with app.state.pool.acquire() as conn:
        #     user_active = await conn.fetchval("SELECT 1 FROM users WHERE user_id = $1 AND deleted_at IS NULL", user_id)
        #     if not user_active:
        #         raise HTTPException(status_code=403, detail="User is inactive or does not exist")
        return user_id, role
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise credentials_exception

async def verify_admin_access(token: str):
    """Ensures the user has 'admin' or 'superadmin' role."""
    user_id, role = await get_current_user_role(token)
    if role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    return user_id, role

async def verify_superadmin_access(token: str):
    """Ensures the user has 'superadmin' role."""
    user_id, role = await get_current_user_role(token)
    if role != "superadmin":
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    return user_id, role

# --- User Management Endpoints ---

@app.post("/register")
async def register_user(name: str, whatsapp: str, expertise: str, password: str, email: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    # Basic validation
    if not all([name, whatsapp, expertise, password, email]):
         raise HTTPException(status_code=400, detail="All fields are required")
    if "@" not in email: # Simple email format check
         raise HTTPException(status_code=400, detail="Invalid email format")

    hashed_password = hash_password(password)
    try:
        # Check if email already exists
        existing_user = await conn.fetchval("SELECT 1 FROM users WHERE email = $1", email)
        if existing_user:
            raise HTTPException(status_code=400, detail="User with this email already exists")

        await conn.execute(
            """
            INSERT INTO users (name, whatsapp, expertise, hashed_password, email, role)
            VALUES ($1, $2, $3, $4, $5, 'tutor') -- Default role 'tutor'
            """,
            name, whatsapp, expertise, hashed_password, email
        )
        return {"message": "User registered successfully"}
    # Catch specific UniqueViolationError if the DB check somehow fails (race condition)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    except Exception as e:
        # Log the error e
        print(f"Error during registration: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred during registration.")


@app.get("/users")
async def get_all_users(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    await verify_admin_access(token) # Ensures admin or superadmin

    users = await conn.fetch("SELECT user_id, name, email, whatsapp, expertise, role, created_at, deleted_at FROM users")
    users_list = [dict(user) for user in users]
    return users_list


@app.get("/users/me")
async def get_current_user(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    user_id, _ = await get_current_user_role(token) # Get user_id from valid token

    user_id_str_to_int = int(user_id) # Convert str to int for DB query

    user = await conn.fetchrow(
        "SELECT user_id, name, email, whatsapp, expertise, role, created_at FROM users WHERE user_id = $1 AND deleted_at IS NULL",
        user_id_str_to_int
    )

    if user is None:
        # This case should ideally not happen if token validation includes DB check,
        # but good as a safeguard.
        raise HTTPException(status_code=404, detail="User not found or inactive")

    return dict(user)


@app.put("/users/{user_id_to_update}")
async def update_user(
    user_id_to_update: int,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection),
    name: Optional[str] = None,
    whatsapp: Optional[str] = None,
    expertise: Optional[str] = None,
    role: Optional[str] = None,
    password: Optional[str] = None
):
    acting_user_id, acting_user_role = await get_current_user_role(token)

    if acting_user_role != "superadmin":
         raise HTTPException(status_code=403, detail="Insufficient permissions to update other users")

    # Check if target user exists
    target_user = await conn.fetchrow("SELECT user_id, email FROM users WHERE user_id = $1", user_id_to_update)
    if not target_user:
        raise HTTPException(status_code=404, detail="User to update not found")

    update_fields = []
    update_values = []
    param_index = 1

    if name is not None:
        update_fields.append(f"name = ${param_index}")
        update_values.append(name)
        param_index += 1
    if whatsapp is not None:
        update_fields.append(f"whatsapp = ${param_index}")
        update_values.append(whatsapp)
        param_index += 1
    if expertise is not None:
        update_fields.append(f"expertise = ${param_index}")
        update_values.append(expertise)
        param_index += 1
    if role is not None:
        # Add validation for allowed roles if necessary
        allowed_roles = ["tutor", "admin", "superadmin"]
        if role not in allowed_roles:
             raise HTTPException(status_code=400, detail=f"Invalid role. Must be one of: {', '.join(allowed_roles)}")
        update_fields.append(f"role = ${param_index}")
        update_values.append(role)
        param_index += 1
    if password is not None:
        if not password: # Prevent setting empty password
             raise HTTPException(status_code=400, detail="Password cannot be empty")
        hashed_password = hash_password(password)
        update_fields.append(f"hashed_password = ${param_index}")
        update_values.append(hashed_password)
        param_index += 1

    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    try:
        query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ${param_index}"
        await conn.execute(query, *update_values, user_id_to_update)
        return {"message": f"User {user_id_to_update} updated successfully"}
    except Exception as e:
        # Log error e
        print(f"Error updating user {user_id_to_update}: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred while updating the user.")


@app.put("/users/me")
async def update_my_profile(
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection),
    name: Optional[str] = None,
    whatsapp: Optional[str] = None,
    expertise: Optional[str] = None,
    password: Optional[str] = None
):
    user_id_from_token, _ = await get_current_user_role(token) # Get user_id from valid token
    user_id_int = int(user_id_from_token)

    update_fields = []
    update_values = []
    param_index = 1

    if name is not None:
        update_fields.append(f"name = ${param_index}")
        update_values.append(name)
        param_index += 1
    if whatsapp is not None:
        update_fields.append(f"whatsapp = ${param_index}")
        update_values.append(whatsapp)
        param_index += 1
    if expertise is not None:
        update_fields.append(f"expertise = ${param_index}")
        update_values.append(expertise)
        param_index += 1
    if password is not None:
        if not password:
             raise HTTPException(status_code=400, detail="Password cannot be empty")
        hashed_password = hash_password(password)
        update_fields.append(f"hashed_password = ${param_index}")
        update_values.append(hashed_password)
        param_index += 1

    if not update_fields:
        return {"message": "No fields provided for update"} # Or raise 400

    try:
        query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ${param_index} AND deleted_at IS NULL"
        result = await conn.execute(query, *update_values, user_id_int)

        # Check if any row was actually updated
        if result == "UPDATE 0":
             raise HTTPException(status_code=404, detail="User not found or already deleted") # Should not happen if token is valid

        return {"message": "Profile updated successfully"}
    except Exception as e:
        # Log error e
        print(f"Error updating profile for user {user_id_from_token}: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred while updating the profile.")

# --- Client Management Endpoints ---

@app.post("/clients", response_model=Client)
async def add_client(client_data: ClientCreate, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    await verify_admin_access(token) # Check permissions

    try:
        # Optional: Check for duplicate email if email is provided and should be unique
        if client_data.email:
            existing = await conn.fetchval("SELECT 1 FROM clients WHERE email = $1 AND deleted_at IS NULL", client_data.email)
            if existing:
                raise HTTPException(status_code=400, detail="Client with this email already exists")

        query = """
            INSERT INTO clients (student_name, student_whatsapp, pj_name, email, pj_whatsapp, school, grade)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING client_id, student_name, student_whatsapp, pj_name, email, pj_whatsapp, school, grade, created_at, deleted_at
        """
        new_client_record = await conn.fetchrow(
            query,
            client_data.student_name, client_data.student_whatsapp, client_data.pj_name,
            client_data.email, client_data.pj_whatsapp, client_data.school, client_data.grade,
        )
        if not new_client_record:
             raise HTTPException(status_code=500, detail="Failed to create client") # Should not happen without error

        return Client(**dict(new_client_record)) # Return the created client data

    except asyncpg.exceptions.UniqueViolationError: # Catch potential unique constraint errors (e.g., if email had a UNIQUE constraint)
        raise HTTPException(status_code=400, detail="Client creation failed due to unique constraint (e.g., email already exists)")
    except HTTPException as e: # Re-raise existing HTTPExceptions
        raise e
    except Exception as e:
        print(f"Error adding client: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


@app.get("/clients", response_model=List[Client])
async def get_all_clients(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    await verify_admin_access(token) # Check permissions

    try:
        clients = await conn.fetch(
            "SELECT client_id, student_name, student_whatsapp, pj_name, email, pj_whatsapp, school, grade, created_at, deleted_at FROM clients WHERE deleted_at IS NULL ORDER BY created_at DESC"
        )
        # Convert records to Pydantic models
        clients_list = [Client(**dict(client)) for client in clients]
        return clients_list
    except Exception as e:
        print(f"Error fetching clients: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


@app.put("/clients/{client_id}", response_model=Client)
async def update_client(client_id: int, client_data: ClientUpdate, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    await verify_admin_access(token) # Check permissions

    # Check if client exists and is not deleted
    current_client = await conn.fetchrow("SELECT * FROM clients WHERE client_id = $1 AND deleted_at IS NULL", client_id)
    if not current_client:
        raise HTTPException(status_code=404, detail="Client not found or has been deleted")

    update_fields = []
    update_values = []
    param_index = 1

    # Use model_dump(exclude_unset=True) to get only provided fields
    update_data = client_data.model_dump(exclude_unset=True)

    # Only check for email uniqueness if email is being updated and not empty/None
    if 'email' in update_data and update_data['email']:
        if update_data['email'] != current_client['email']:
            existing = await conn.fetchval(
                "SELECT 1 FROM clients WHERE email = $1 AND client_id != $2 AND deleted_at IS NULL",
                update_data['email'], client_id
            )
            if existing:
                raise HTTPException(status_code=400, detail="Another client with this email already exists")

    for key, value in update_data.items():
        update_fields.append(f"{key} = ${param_index}")
        update_values.append(value)
        param_index += 1

    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    try:
        query = f"UPDATE clients SET {', '.join(update_fields)} WHERE client_id = ${param_index} RETURNING client_id, student_name, student_whatsapp, pj_name, email, pj_whatsapp, school, grade, created_at, deleted_at"
        updated_client_record = await conn.fetchrow(query, *update_values, client_id)

        if not updated_client_record:
            raise HTTPException(status_code=404, detail="Client not found during update")

        return Client(**dict(updated_client_record))

    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(status_code=400, detail="Client update failed due to unique constraint (e.g., email already exists)")
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error updating client {client_id}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

@app.delete("/clients/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(client_id: int, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Soft delete a client by setting the deleted_at timestamp."""
    await verify_admin_access(token) # Check permissions

    # Check if client exists and is not already deleted
    client_exists = await conn.fetchval("SELECT 1 FROM clients WHERE client_id = $1 AND deleted_at IS NULL", client_id)
    if not client_exists:
        raise HTTPException(status_code=404, detail="Client not found or already deleted")

    try:
        await conn.execute(
            "UPDATE clients SET deleted_at = NOW() WHERE client_id = $1",
            client_id
        )
        # No content to return on successful delete
        return None
    except Exception as e:
        print(f"Error deleting client {client_id}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


# --- Tutoring Job Management Endpoints ---

@app.post("/jobs", response_model=Job, status_code=status.HTTP_201_CREATED)
async def create_tutoring_job(job_data: JobCreate, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Creates a new tutoring job."""
    await verify_admin_access(token) # Ensure user is admin or superadmin

    # Optional: Validate if mentor_ids and client_ids exist in their respective tables
    # This adds overhead but ensures data integrity. Example for mentors:
    # if job_data.mentor_ids:
    #     valid_mentors = await conn.fetchval("SELECT count(*) FROM users WHERE user_id = ANY($1::int[]) AND deleted_at IS NULL", job_data.mentor_ids)
    #     if valid_mentors != len(job_data.mentor_ids):
    #         raise HTTPException(status_code=400, detail="One or more mentor IDs are invalid or refer to deleted users")
    # Similar check for client_ids...

    try:
        query = """
            INSERT INTO tutoring_jobs (program_title, mentor_ids, client_ids, client_rate, mentor_rate, rate_type)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING job_id, program_title, mentor_ids, client_ids, client_rate, mentor_rate, rate_type, created_at, deleted_at
        """
        new_job_record = await conn.fetchrow(
            query,
            job_data.program_title,
            job_data.mentor_ids,
            job_data.client_ids,
            job_data.client_rate,
            job_data.mentor_rate,
            job_data.rate_type,
        )
        if not new_job_record:
            raise HTTPException(status_code=500, detail="Failed to create tutoring job")

        # Convert numeric types from Decimal (asyncpg default) to float for Pydantic model
        job_dict = dict(new_job_record)
        job_dict['client_rate'] = float(job_dict['client_rate'])
        job_dict['mentor_rate'] = float(job_dict['mentor_rate'])

        return Job(**job_dict)

    except asyncpg.exceptions.ForeignKeyViolationError as e:
         # This would trigger if mentor_ids/client_ids had FK constraints
         raise HTTPException(status_code=400, detail=f"Invalid mentor or client ID provided: {e}")
    except Exception as e:
        print(f"Error creating tutoring job: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

# What to add (preferably grouped with other /jobs endpoints):

@app.post("/jobs/request", response_model=Job, status_code=status.HTTP_201_CREATED)
async def tutor_request_job(
    request_data: JobRequestCreate,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """Allows a tutor to request a new job. Admin will verify later."""
    user_id_str, user_role = await get_current_user_role(token)
    # Any authenticated user (tutor, admin, superadmin) can request, but typically tutors.
    # if user_role not in ["tutor", "admin", "superadmin"]: 
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions to request a job.")
    
    requesting_tutor_id = int(user_id_str)

    composite_program_title = _construct_requested_job_title(
        request_data.program_name_actual,
        request_data.student_names_text
    )

    try:
        query = """
            INSERT INTO tutoring_jobs (
                program_title, mentor_ids, client_ids, 
                client_rate, mentor_rate, rate_type
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING job_id, program_title, mentor_ids, client_ids, 
                      client_rate, mentor_rate, rate_type, created_at, deleted_at
        """
        # For requested jobs: client_ids is empty, rates are 0, rate_type is NULL
        new_job_record = await conn.fetchrow(
            query,
            composite_program_title,
            [requesting_tutor_id], # mentor_ids is a list containing the requester
            [],                   # client_ids is empty list
            0.0,                  # client_rate
            0.0,                  # mentor_rate
            None                  # rate_type is NULL initially
        )
        if not new_job_record:
            raise HTTPException(status_code=500, detail="Failed to create requested job.")

        job_dict = dict(new_job_record)
        job_dict['client_rate'] = float(job_dict['client_rate'])
        job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
        # job_dict['rate_type'] will be None, which is fine for Optional[str]

        return Job(**job_dict)

    except Exception as e:
        print(f"Error creating requested job: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


@app.get("/jobs/requested", response_model=List[Job])
async def list_requested_jobs(
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """Lists all jobs that are pending admin finalization (marked with '[REQUESTED]')."""
    await verify_admin_access(token) # Ensure admin or superadmin

    try:
        # The LIKE pattern should match the end of the string
        # Using '% | [REQUESTED]' or '%|[REQUESTED]' to be more flexible with spaces
        jobs_db = await conn.fetch(
            """
            SELECT job_id, program_title, mentor_ids, client_ids, 
                   client_rate, mentor_rate, rate_type, created_at, deleted_at
            FROM tutoring_jobs
            WHERE (program_title LIKE '% | [REQUESTED]' OR program_title LIKE '%|[REQUESTED]') 
              AND deleted_at IS NULL
            ORDER BY created_at DESC
            """
        )
        jobs_list = []
        for job_record in jobs_db:
            job_dict = dict(job_record)
            job_dict['client_rate'] = float(job_dict['client_rate'])
            job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
            jobs_list.append(Job(**job_dict))
        return jobs_list
    except Exception as e:
        print(f"Error fetching requested jobs: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


@app.put("/jobs/requested/{job_id_to_finalize}", response_model=Job)
async def finalize_requested_job(
    job_id_to_finalize: int,
    job_final_data: JobRequestedFinalize,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """Admin finalizes a requested job, updating it and its related attendance records."""
    await verify_admin_access(token)

    async with conn.transaction():
        # 1. Fetch and validate the job
        current_job = await conn.fetchrow(
            "SELECT job_id, program_title FROM tutoring_jobs WHERE job_id = $1 AND deleted_at IS NULL FOR UPDATE",
            job_id_to_finalize
        )
        if not current_job:
            raise HTTPException(status_code=404, detail="Job to finalize not found or already deleted.")

        _actual_title, _students_text, is_requested = _parse_requested_job_title(current_job['program_title'])
        if not is_requested:
            raise HTTPException(status_code=400, detail="This job is not in a 'requested' state. Cannot finalize using this endpoint.")

        # 2. Update the job record
        updated_job_record = await conn.fetchrow(
            """
            UPDATE tutoring_jobs
            SET program_title = $1, mentor_ids = $2, client_ids = $3,
                client_rate = $4, mentor_rate = $5, rate_type = $6
            WHERE job_id = $7
            RETURNING job_id, program_title, mentor_ids, client_ids, 
                      client_rate, mentor_rate, rate_type, created_at, deleted_at
            """,
            job_final_data.program_title, job_final_data.mentor_ids, job_final_data.client_ids,
            job_final_data.client_rate, job_final_data.mentor_rate, job_final_data.rate_type,
            job_id_to_finalize
        )
        if not updated_job_record:
            raise HTTPException(status_code=500, detail="Failed to update job during finalization.")

        # 3. Fetch actual student names for the new client_ids
        final_student_names_list = []
        if job_final_data.client_ids:
            clients_query_result = await conn.fetch(
                """
                SELECT student_name FROM clients 
                WHERE client_id = ANY($1::int[]) AND deleted_at IS NULL
                ORDER BY array_position($1::int[], client_id);
                """,
                job_final_data.client_ids
            )
            final_student_names_list = [record['student_name'] for record in clients_query_result]
        
        # 4. Update all related non-deleted attendance records
        await conn.execute(
            """
            UPDATE attendance
            SET program_title = $1, client_ids = $2, student_names = $3,
                client_rate = $4, mentor_rate = $5, rate_type = $6
            WHERE job_id = $7 AND deleted_at IS NULL
            """,
            job_final_data.program_title, # Corrected program title
            job_final_data.client_ids,   # Corrected client_ids
            final_student_names_list,    # Corrected student_names
            job_final_data.client_rate,  # Corrected client_rate
            job_final_data.mentor_rate,  # Corrected mentor_rate
            job_final_data.rate_type,    # Corrected rate_type
            job_id_to_finalize
        )

        job_dict = dict(updated_job_record)
        job_dict['client_rate'] = float(job_dict['client_rate'])
        job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
        return Job(**job_dict)


@app.delete("/jobs/requested/{job_id_to_reject}", status_code=status.HTTP_204_NO_CONTENT)
async def reject_requested_job(
    job_id_to_reject: int,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """Admin rejects a requested job. Soft-deletes the job and its related attendance records."""
    await verify_admin_access(token)

    async with conn.transaction():
        # 1. Fetch and validate the job
        job_to_reject = await conn.fetchrow(
            "SELECT job_id, program_title FROM tutoring_jobs WHERE job_id = $1 AND deleted_at IS NULL FOR UPDATE",
            job_id_to_reject
        )
        if not job_to_reject:
            raise HTTPException(status_code=404, detail="Job to reject not found or already deleted.")

        _actual_title, _students_text, is_requested = _parse_requested_job_title(job_to_reject['program_title'])
        if not is_requested:
            raise HTTPException(status_code=400, detail="This job is not in a 'requested' state. Cannot reject using this endpoint.")

        # 2. Soft-delete the job
        job_delete_result = await conn.execute(
            "UPDATE tutoring_jobs SET deleted_at = NOW() WHERE job_id = $1",
            job_id_to_reject
        )
        if job_delete_result == "UPDATE 0":
            # Should not happen if fetched above, but as a safeguard
            raise HTTPException(status_code=404, detail="Job to reject not found during delete operation.")

        # 3. Soft-delete related attendance records
        # We don't need to check if any were updated, just perform the action.
        await conn.execute(
            "UPDATE attendance SET deleted_at = NOW() WHERE job_id = $1 AND deleted_at IS NULL",
            job_id_to_reject
        )
        return None # HTTP 204 No Content

@app.get("/jobs", response_model=List[Job])
async def get_all_tutoring_jobs(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Retrieves a list of all non-deleted tutoring jobs."""
    await verify_admin_access(token) # Ensure user is admin or superadmin

    try:
        jobs = await conn.fetch(
            """
            SELECT job_id, program_title, mentor_ids, client_ids, client_rate, mentor_rate, rate_type, created_at, deleted_at
            FROM tutoring_jobs
            WHERE deleted_at IS NULL
            ORDER BY created_at DESC
            """
        )

        # Convert records to Pydantic models, handling numeric conversion
        jobs_list = []
        for job in jobs:
            job_dict = dict(job)
            job_dict['client_rate'] = float(job_dict['client_rate'])
            job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
            jobs_list.append(Job(**job_dict))

        return jobs_list
    except Exception as e:
        print(f"Error fetching tutoring jobs: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

@app.get("/jobs/me", response_model=List[MyJobDetail]) # Changed response_model
async def get_my_tutoring_jobs(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """
    Retrieves all non-deleted tutoring jobs assigned to the current user (as a mentor),
    including basic details of associated clients. Client rate is excluded.
    """
    user_id_str, _ = await get_current_user_role(token)
    
    try:
        user_id_int = int(user_id_str)
    except ValueError:
        raise HTTPException(status_code=401, detail="Invalid user ID format in token")

    try:
        # Updated SQL query to fetch job details and aggregate client information
        query = """
            SELECT
                tj.job_id,
                tj.program_title,
                tj.mentor_ids,
                tj.client_ids, -- Kept for reference, can be removed from MyJobDetail if not needed
                tj.mentor_rate,
                tj.rate_type,
                tj.created_at,
                tj.deleted_at,
                COALESCE(
                    (
                        SELECT json_agg(
                            json_build_object(
                                'student_name', c.student_name,
                                'student_whatsapp', c.student_whatsapp,
                                'school', c.school,
                                'grade', c.grade
                                -- 'client_id', c.client_id -- Uncomment if you add client_id to ClientBasicInfo
                            ) ORDER BY c.student_name -- Optional: order clients
                        )
                        FROM clients c
                        WHERE c.client_id = ANY(tj.client_ids) AND c.deleted_at IS NULL
                    ),
                    '[]'::json
                ) AS client_details_json
            FROM
                tutoring_jobs tj
            WHERE
                $1 = ANY(tj.mentor_ids) AND tj.deleted_at IS NULL
            ORDER BY
                tj.created_at DESC;
        """
        
        job_records = await conn.fetch(query, user_id_int)

        jobs_list_with_clients = []
        for record in job_records:
            job_dict = dict(record)
            
            # Parse the client_details_json string into a list of ClientBasicInfo objects
            client_details_list = []
            if job_dict.get('client_details_json'):
                # asyncpg returns json/jsonb as string, so parse it
                clients_data = json.loads(job_dict['client_details_json']) 
                for client_data in clients_data:
                    client_details_list.append(ClientBasicInfo(**client_data))
            
            # Construct the MyJobDetail object
            # Note: client_rate is not selected in the query and not part of MyJobDetail
            job_detail = MyJobDetail(
                job_id=job_dict['job_id'],
                program_title=job_dict['program_title'],
                mentor_ids=job_dict['mentor_ids'],
                mentor_rate=float(job_dict['mentor_rate']), # Ensure float conversion
                rate_type=job_dict['rate_type'],
                created_at=job_dict['created_at'],
                deleted_at=job_dict.get('deleted_at'), # Use .get() for optional fields
                clients=client_details_list,
                # client_ids=job_dict['client_ids'], 
            )
            jobs_list_with_clients.append(job_detail)

        return jobs_list_with_clients
        
    except HTTPException as e:
        raise e
    except json.JSONDecodeError as e:
        print(f"Error decoding client_details_json for user {user_id_int}: {e}")
        raise HTTPException(status_code=500, detail="Error processing client data.")
    except Exception as e:
        print(f"Error fetching jobs for user {user_id_int}: {e}")
        # Consider logging the full traceback for e
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred while retrieving your jobs.")

@app.get("/jobs/{job_id}", response_model=Job)
async def get_tutoring_job(job_id: int, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Retrieves details of a specific non-deleted tutoring job."""
    await verify_admin_access(token) # Ensure user is admin or superadmin

    try:
        job = await conn.fetchrow(
            """
            SELECT job_id, program_title, mentor_ids, client_ids, client_rate, mentor_rate, rate_type, created_at, deleted_at
            FROM tutoring_jobs
            WHERE job_id = $1 AND deleted_at IS NULL
            """,
            job_id
        )

        if not job:
            raise HTTPException(status_code=404, detail="Tutoring job not found or has been deleted")

        # Convert numeric types and return
        job_dict = dict(job)
        job_dict['client_rate'] = float(job_dict['client_rate'])
        job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
        return Job(**job_dict)

    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error fetching tutoring job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


@app.put("/jobs/{job_id}", response_model=Job)
async def update_tutoring_job(job_id: int, job_data: JobUpdate, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Updates an existing tutoring job."""
    await verify_admin_access(token) # Ensure user is admin or superadmin

    # Check if job exists and is not deleted
    current_job = await conn.fetchrow("SELECT job_id FROM tutoring_jobs WHERE job_id = $1 AND deleted_at IS NULL", job_id)
    if not current_job:
        raise HTTPException(status_code=404, detail="Tutoring job not found or has been deleted")

    update_fields = []
    update_values = []
    param_index = 1

    # Use model_dump(exclude_unset=True) to get only provided fields
    update_data = job_data.model_dump(exclude_unset=True)

    # Optional: Validate mentor/client IDs if they are being updated
    # if 'mentor_ids' in update_data and update_data['mentor_ids'] is not None:
    #     # Add validation logic similar to create endpoint
    # if 'client_ids' in update_data and update_data['client_ids'] is not None:
    #     # Add validation logic similar to create endpoint

    for key, value in update_data.items():
        update_fields.append(f"{key} = ${param_index}")
        update_values.append(value)
        param_index += 1

    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    try:
        query = f"""
            UPDATE tutoring_jobs
            SET {', '.join(update_fields)}
            WHERE job_id = ${param_index}
            RETURNING job_id, program_title, mentor_ids, client_ids, client_rate, mentor_rate, rate_type, created_at, deleted_at
        """
        updated_job_record = await conn.fetchrow(query, *update_values, job_id)

        if not updated_job_record:
             # Should not happen if the initial check passed
             raise HTTPException(status_code=404, detail="Tutoring job not found during update")

        # Convert numeric types and return
        job_dict = dict(updated_job_record)
        job_dict['client_rate'] = float(job_dict['client_rate'])
        job_dict['mentor_rate'] = float(job_dict['mentor_rate'])
        return Job(**job_dict)

    except asyncpg.exceptions.ForeignKeyViolationError as e:
         raise HTTPException(status_code=400, detail=f"Invalid mentor or client ID provided during update: {e}")
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error updating tutoring job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


@app.delete("/jobs/{job_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tutoring_job(job_id: int, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Soft delete a tutoring job by setting the deleted_at timestamp."""
    await verify_admin_access(token) # Ensure user is admin or superadmin

    # Check if job exists and is not already deleted
    job_exists = await conn.fetchval("SELECT 1 FROM tutoring_jobs WHERE job_id = $1 AND deleted_at IS NULL", job_id)
    if not job_exists:
        raise HTTPException(status_code=404, detail="Tutoring job not found or already deleted")

    try:
        result = await conn.execute(
            "UPDATE tutoring_jobs SET deleted_at = NOW() WHERE job_id = $1",
            job_id
        )
        # Check if the update actually affected a row
        if result == "UPDATE 0":
             # This could happen in a race condition if deleted between check and update
             raise HTTPException(status_code=404, detail="Tutoring job not found or already deleted")

        # No content to return on successful delete
        return None
    except Exception as e:
        print(f"Error deleting tutoring job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

@app.get("/attendance", response_model=List[AdminAttendanceRecord])
async def get_all_attendance_records(
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Retrieves all attendance records.
    Accessible only by admin or superadmin.
    Includes client_rate.
    """
    # 1. Verify admin or superadmin access
    await verify_admin_access(token) # This helper function already checks roles

    # 2. Fetch all non-deleted attendance records
    try:
        # Select all fields required for AdminAttendanceRecord
        query = """
            SELECT
                record_id, job_id, session_date, start_time, end_time, duration,
                activity_log, photo_proof_url,
                tutor_id, tutor_name, client_ids, student_names, program_title,
                status, mentor_rate, client_rate, rate_type, 
                invoice_ids, payslip_id,
                submitted_at, deleted_at
            FROM attendance
            WHERE deleted_at IS NULL
            ORDER BY submitted_at DESC, session_date DESC; -- Or any other preferred order
        """
        all_attendance_db = await conn.fetch(query)

        admin_attendance_list = []
        for record_db in all_attendance_db:
            record_dict = dict(record_db)
            
            # Convert NUMERIC rates from Decimal to float for Pydantic model
            if record_dict.get('mentor_rate') is not None:
                record_dict['mentor_rate'] = float(record_dict['mentor_rate'])
            if record_dict.get('client_rate') is not None:
                record_dict['client_rate'] = float(record_dict['client_rate'])
            
            admin_attendance_list.append(AdminAttendanceRecord(**record_dict))
            
        return admin_attendance_list

    except HTTPException as e:
        # Re-raise HTTPExceptions from verify_admin_access
        raise e
    except Exception as e:
        print(f"Error fetching all attendance records for admin: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.get("/attendance/eligible-for-invoice", response_model=List[EligibleAttendanceRecordInfo])
async def get_eligible_attendance_for_invoicing(
    cutoff_date: date, # Query parameter
    token: str,        # Query parameter
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Retrieves attendance records that are eligible for invoicing.
    Eligibility criteria:
    - Status is 'approved'.
    - Session date is on or before the cutoff_date.
    - The record has not yet been included in any invoice (invoice_ids is NULL or empty).
    - Accessible only by superadmin.
    """
    # 1. Verify superadmin access
    user_id_str, user_role = await get_current_user_role(token)
    if user_role != "superadmin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Superadmin access required.")

    # 2. Fetch eligible attendance records
    
    try:
        query = """
            SELECT
                record_id,
                student_names, -- This is already an array in your attendance table
                program_title,
                session_date
                -- client_ids -- Uncomment if you add client_ids to EligibleAttendanceRecordInfo
            FROM attendance
            WHERE
                status = 'approved'
                AND session_date <= $1
                AND (invoice_ids IS NULL OR cardinality(invoice_ids) = 0) -- Not yet part of any invoice
                AND deleted_at IS NULL
            ORDER BY
                session_date ASC, record_id ASC; -- Or any other preferred order
        """
        eligible_records_db = await conn.fetch(query, cutoff_date)

        # Convert records to Pydantic models
        eligible_records_list = [EligibleAttendanceRecordInfo(**dict(record)) for record in eligible_records_db]
        
        return eligible_records_list

    except HTTPException as e:
        # Re-raise HTTPExceptions from verify_superadmin_access (or get_current_user_role)
        raise e
    except Exception as e:
        print(f"Error fetching eligible attendance records: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.put("/attendance/adminedit/{record_id}", response_model=AdminAttendanceRecord)
async def admin_edit_attendance_record(
    record_id: int,
    update_data: AdminAttendanceUpdate,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Allows an admin or superadmin to edit specific fields of an attendance record:
    session_date, start_time, end_time, client_rate, mentor_rate, status.
    """
    # 1. Verify admin or superadmin access
    await verify_admin_access(token) # This helper function already checks roles

    # 2. Fetch the current attendance record to ensure it exists
    # We don't strictly need its current values for this update, just existence.
    record_exists = await conn.fetchval(
        "SELECT 1 FROM attendance WHERE record_id = $1 AND deleted_at IS NULL",
        record_id
    )
    if not record_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found or has been deleted.")

    # 3. Prepare the update query
    update_payload = update_data.model_dump(exclude_unset=True) # Get only fields that were actually sent

    if not update_payload:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No fields provided for update.")

    # If either start_time or end_time is being updated, we need both to recalculate duration
    if 'start_time' in update_payload or 'end_time' in update_payload:
        # Get the current record's times if not being updated
        current_times = await conn.fetchrow(
            "SELECT start_time, end_time FROM attendance WHERE record_id = $1",
            record_id
        )
        
        # Use new values if provided, otherwise use current values
        start_time = update_payload.get('start_time', current_times['start_time'])
        end_time = update_payload.get('end_time', current_times['end_time'])
        
        # Calculate new duration
        start_hours = start_time.hour + (start_time.minute / 60)
        end_hours = end_time.hour + (end_time.minute / 60)
        duration_hours = end_hours - start_hours
        duration_minutes = int(duration_hours * 60)  # Convert to minutes and round to integer
        
        # Add or update duration in the payload
        update_payload['duration'] = str(duration_minutes)

    set_clauses = []
    update_values = []
    param_idx = 1

    # Iterate through the editable fields defined in AdminAttendanceUpdate
    for key, value in update_payload.items():
        # Pydantic model AdminAttendanceUpdate already restricts keys to the editable ones
        set_clauses.append(f"{key} = ${param_idx}")
        update_values.append(value)
        param_idx += 1
    
    if not set_clauses: # Should not happen if update_payload is not empty and keys are valid
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No valid fields to update.")

    query_set_string = ", ".join(set_clauses)
    
    # Add record_id to the end of update_values for the WHERE clause
    update_values.append(record_id)

    # Construct the full SQL query
    # RETURNING all fields needed for AdminAttendanceRecord
    sql_update_query = f"""
        UPDATE attendance
        SET {query_set_string}
        WHERE record_id = ${param_idx} AND deleted_at IS NULL
        RETURNING record_id, job_id, session_date, start_time, end_time, duration,
                  activity_log, photo_proof_url, tutor_id, tutor_name, client_ids,
                  student_names, program_title, status, mentor_rate, client_rate,
                  rate_type, submitted_at, deleted_at;
    """

    try:
        updated_record_db = await conn.fetchrow(sql_update_query, *update_values)

        if not updated_record_db:
            # This could happen if the record was deleted between the initial fetch and the update,
            # or if record_id was invalid (though initial check should catch this).
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found during update, or no changes made.")

        record_dict = dict(updated_record_db)
        
        # Convert NUMERIC rates from Decimal to float for Pydantic model
        if record_dict.get('mentor_rate') is not None:
            record_dict['mentor_rate'] = float(record_dict['mentor_rate'])
        if record_dict.get('client_rate') is not None:
            record_dict['client_rate'] = float(record_dict['client_rate'])
            
        return AdminAttendanceRecord(**record_dict)

    except asyncpg.exceptions.DataError as e: # Catch errors like invalid date/time format or invalid enum value for status
        print(f"DataError during admin attendance update: {e}")
        # Check if the error is about the status enum if you have a CHECK constraint or enum type in DB
        if "status" in str(e).lower() and hasattr(e, 'sqlstate') and e.sqlstate == '23514': # Check constraint violation
             raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid status value. Must be one of 'pending', 'approved', 'rejected'.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid data format: {e}")
    except Exception as e:
        print(f"Error during admin update of attendance record {record_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.put("/attendance/approval/{record_id}", response_model=AttendanceStatusUpdateResponse)
async def update_attendance_status(
    record_id: int,
    status: Literal["pending", "approved", "rejected"], # Query parameter with allowed values
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Updates the status of an attendance record (pending, approved, rejected).
    Accessible only by admin or superadmin.
    """
    # 1. Verify admin or superadmin access
    await verify_admin_access(token) # This helper function already checks roles

    # 2. Check if the attendance record exists and is not deleted
    record_exists = await conn.fetchval(
        "SELECT 1 FROM attendance WHERE record_id = $1 AND deleted_at IS NULL",
        record_id
    )
    if not record_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found or has been deleted.")

    # 3. Update the status
    try:
        # The status parameter is already validated by FastAPI due to Literal type hint
        result = await conn.execute(
            "UPDATE attendance SET status = $1 WHERE record_id = $2 AND deleted_at IS NULL",
            status, record_id
        )

        if result == "UPDATE 0": # Should not happen if record_exists check passed, but good safeguard
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found during update or status was already set to the same value.")
        
        return AttendanceStatusUpdateResponse(record_id=record_id, status=status)

    except Exception as e:
        print(f"Error updating attendance status for record {record_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.get("/attendance/me", response_model=List[MyAttendanceRecord])
async def get_my_attendance_history(
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Retrieves the attendance history for the currently authenticated user (tutor).
    Excludes client_rate from the response.
    """
    user_id_str, _ = await get_current_user_role(token) # Get user_id and role

    try:
        tutor_id_int = int(user_id_str)
    except ValueError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid user ID format in token")

    try:
        query = """
            SELECT
                record_id, job_id, session_date, start_time, end_time, duration,
                activity_log, photo_proof_url,
                tutor_id, tutor_name, client_ids, student_names, program_title,
                status, mentor_rate, client_rate, rate_type, 
                invoice_ids, payslip_id,
                submitted_at, deleted_at
            FROM attendance
            WHERE tutor_id = $1 AND deleted_at IS NULL
            ORDER BY session_date DESC, start_time DESC;
        """
        attendance_records_db = await conn.fetch(query, tutor_id_int)

        my_attendance_list = []
        for record_db in attendance_records_db:
            record_dict = dict(record_db)
            
            # Convert NUMERIC mentor_rate from Decimal to float for Pydantic model
            if record_dict.get('mentor_rate') is not None:
                record_dict['mentor_rate'] = float(record_dict['mentor_rate'])
            
            # client_rate is not in record_dict because it wasn't selected
            
            my_attendance_list.append(MyAttendanceRecord(**record_dict))
            
        return my_attendance_list

    except HTTPException as e:
        # Re-raise HTTPExceptions from get_current_user_role
        raise e
    except Exception as e:
        print(f"Error fetching attendance history for tutor {tutor_id_int}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.post("/attendance/uploadproof")
async def upload_attendance_proof(token: str, file: UploadFile = File(...)):
    """
    Uploads a proof photo for an attendance record.
    Saves the file locally with a unique name and returns the filename.
    Requires a valid token for authorization.
    """
    # Verify token to ensure only authenticated users can upload
    # You can use get_current_user_role or a simpler check if role doesn't matter for upload itself
    try:
        await get_current_user_role(token) # Ensures token is valid
    except HTTPException as e:
        raise e # Re-raise auth errors

    # Create the upload directory if it doesn't exist
    os.makedirs(UPLOAD_PROOF_DIR, exist_ok=True)

    # Sanitize filename and get extension
    original_filename = file.filename
    # A basic sanitization: remove path components, keep only the actual filename part
    base_filename = os.path.basename(original_filename) 
    # Get the file extension
    _, extension = os.path.splitext(base_filename)
    
    if not extension: # Handle files without extensions if necessary, or reject them
        extension = "" # Or raise HTTPException(status_code=400, detail="File has no extension")

    # Generate a unique filename
    unique_filename = f"{uuid.uuid4()}{extension}"
    file_path = os.path.join(UPLOAD_PROOF_DIR, unique_filename)

    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        print(f"Error saving uploaded proof file: {e}")
        raise HTTPException(status_code=500, detail="Could not save proof file.")
    finally:
        await file.close() # Ensure the file is closed

    # The client will use this filename as 'photo_proof_url'
    # when submitting the full attendance record.
    return {"filename": unique_filename, "message": "File uploaded successfully"}

@app.post("/attendance/{job_id}", response_model=AttendanceRecord, status_code=status.HTTP_201_CREATED) # Path changed
async def create_attendance_record(
    job_id: int, # job_id now a path parameter
    attendance_data: AttendanceCreate, # Model updated
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Creates a new attendance record for a tutoring session.
    job_id is taken from the path.
    submitted_at is handled by the database.
    """
    user_id_str, user_role = await get_current_user_role(token)
    
    try:
        tutor_id_int = int(user_id_str)
    except ValueError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid user ID format in token")

    tutor_user = await conn.fetchrow("SELECT name FROM users WHERE user_id = $1 AND deleted_at IS NULL", tutor_id_int)
    if not tutor_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tutor not found or inactive.")
    tutor_name = tutor_user['name']


    job_details_raw = await conn.fetchrow(
        """
        SELECT program_title, client_ids, client_rate, mentor_rate, rate_type 
        FROM tutoring_jobs 
        WHERE job_id = $1 AND deleted_at IS NULL
        """,
        job_id 
    )
    if not job_details_raw:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job with ID {job_id} not found or has been deleted.")

    # Parse job title for requested jobs
    job_program_title_from_db = job_details_raw['program_title']
    actual_job_program_title, student_names_from_job_title_str, is_requested_job = _parse_requested_job_title(job_program_title_from_db)
    
    # Prepare data for attendance record
    attendance_program_title = actual_job_program_title if is_requested_job and actual_job_program_title else job_program_title_from_db
    attendance_client_ids = []
    attendance_student_names_list = []
    
    if is_requested_job:
        if student_names_from_job_title_str:
            # Split by comma or ampersand, then strip whitespace
            raw_names = student_names_from_job_title_str.replace('&', ',').split(',')
            attendance_student_names_list = [name.strip() for name in raw_names if name.strip()]
        # client_ids remain empty for requested job's attendance initially
        # rates are taken from the temporary job (which are 0)
    else: # Regular job
        job_client_ids = job_details_raw['client_ids']
        if not job_client_ids:
            # For a non-requested job, client_ids should ideally exist.
            # Depending on strictness, you might raise an error or allow it.
            # For now, let's proceed, but this indicates a data setup issue for a regular job.
            print(f"Warning: Regular job ID {job_id} has no associated client_ids.")
        else:
            attendance_client_ids = job_client_ids
            clients_query_result = await conn.fetch(
                """
                SELECT student_name 
                FROM clients 
                WHERE client_id = ANY($1::int[]) AND deleted_at IS NULL
                ORDER BY array_position($1::int[], client_id);
                """,
                job_client_ids
            )
            attendance_student_names_list = [record['student_name'] for record in clients_query_result]
            if len(attendance_student_names_list) != len(job_client_ids):
                print(f"Warning: Mismatch in client_ids count and fetched student_names for job_id {job_id}.")

    status = "pending"

    try:
        insert_query = """
            INSERT INTO attendance (
                job_id, session_date, start_time, end_time, duration, activity_log, photo_proof_url,
                tutor_id, tutor_name, client_ids, student_names, program_title,
                status, mentor_rate, client_rate, rate_type 
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
            ) RETURNING record_id, job_id, session_date, start_time, end_time, duration, activity_log,
                        photo_proof_url, tutor_id, tutor_name, client_ids, student_names, program_title,
                        status, mentor_rate, client_rate, rate_type, submitted_at, deleted_at, invoice_ids, payslip_id; 
        """
        # Convert time objects to hour decimal values directly
        start_hours = attendance_data.start_time.hour + (attendance_data.start_time.minute / 60)
        end_hours = attendance_data.end_time.hour + (attendance_data.end_time.minute / 60)
        duration_hours = end_hours - start_hours
        duration_minutes = int(duration_hours * 60)  # Convert to minutes and round to integer

        new_attendance_record = await conn.fetchrow(
            insert_query,
            job_id, 
            attendance_data.session_date, attendance_data.start_time, attendance_data.end_time,
            str(duration_minutes), attendance_data.activity_log, attendance_data.photo_proof_url,
            tutor_id_int, tutor_name, 
            attendance_client_ids, # Use the determined client_ids
            attendance_student_names_list, # Use the determined student_names
            attendance_program_title, # Use the determined program_title
            status, 
            job_details_raw['mentor_rate'], # Will be 0 for requested job
            job_details_raw['client_rate'], # Will be 0 for requested job
            job_details_raw['rate_type'] # Will be NULL for requested job
        )
    # ... (rest of the function: error handling, response model conversion) ...
    # Ensure AttendanceRecord model can handle Optional rate_type if it's not already.
    # The AttendanceRecord model already has Optional for rates and rate_type, which is good.

        if not new_attendance_record:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create attendance record.")

        record_dict = dict(new_attendance_record)
        # Ensure numeric fields are float
        if record_dict.get('mentor_rate') is not None:
            record_dict['mentor_rate'] = float(record_dict['mentor_rate'])
        if record_dict.get('client_rate') is not None:
            record_dict['client_rate'] = float(record_dict['client_rate'])
        
        # Ensure invoice_ids is a list, even if null from DB
        if record_dict.get('invoice_ids') is None:
            record_dict['invoice_ids'] = []

        return AttendanceRecord(**record_dict)

    except asyncpg.exceptions.ForeignKeyViolationError as e:
        print(f"ForeignKeyViolationError: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid reference: {e.detail or e.message}")
    except Exception as e:
        print(f"Error creating attendance record: {e}") # Consider logging traceback
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

@app.put("/attendance/{record_id}", response_model=MyAttendanceRecord) # Using MyAttendanceRecord to exclude client_rate
async def update_attendance_record(
    record_id: int,
    attendance_data: AttendanceUpdate,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Updates an existing attendance record.
    - Admins/Superadmins can update any record.
    - Tutors can only update their own submitted records.
    - Editable fields: session_date, start_time, end_time, duration, activity_log.
    """
    user_id_str, user_role = await get_current_user_role(token)

    try:
        current_user_id_int = int(user_id_str)
    except ValueError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid user ID format in token")

    # 1. Fetch the current attendance record to check ownership and existence
    current_attendance = await conn.fetchrow(
        "SELECT tutor_id, client_rate FROM attendance WHERE record_id = $1 AND deleted_at IS NULL", # Fetch client_rate for admin response if needed, but MyAttendanceRecord will strip it
        record_id
    )

    if not current_attendance:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found or has been deleted.")

    if current_attendance['invoice_ids'] and len(current_attendance['invoice_ids']) > 0:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, # Or 409 Conflict
            detail="This attendance record cannot be edited because it is linked to an invoice. Manual invoice adjustment or cancellation is required first."
        )

    # 2. Authorization Check
    if user_role not in ["admin", "superadmin"]:
        if current_attendance['tutor_id'] != current_user_id_int:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this attendance record."
            )

    # 3. Prepare the update query
    update_payload = attendance_data.model_dump(exclude_unset=True) # Get only fields that were actually sent

    if not update_payload:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No fields provided for update.")

    # If either start_time or end_time is being updated, we need both to recalculate duration
    if 'start_time' in update_payload or 'end_time' in update_payload:
        # Get the current record's times if not being updated
        current_times = await conn.fetchrow(
            "SELECT start_time, end_time FROM attendance WHERE record_id = $1",
            record_id
        )
        
        # Use new values if provided, otherwise use current values
        start_time = update_payload.get('start_time', current_times['start_time'])
        end_time = update_payload.get('end_time', current_times['end_time'])
        
        # Calculate new duration
        start_hours = start_time.hour + (start_time.minute / 60)
        end_hours = end_time.hour + (end_time.minute / 60)
        duration_hours = end_hours - start_hours
        duration_minutes = int(duration_hours * 60)  # Convert to minutes and round to integer
        
        # Add or update duration in the payload
        update_payload['duration'] = str(duration_minutes)

    set_clauses = []
    update_values = []
    param_idx = 1

    # Iterate through the editable fields defined in AttendanceUpdate
    for key, value in update_payload.items():
        # Pydantic model AttendanceUpdate already restricts keys to the editable ones
        set_clauses.append(f"{key} = ${param_idx}")
        update_values.append(value)
        param_idx += 1
    
    if not set_clauses: # Should not happen if update_payload is not empty and keys are valid
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No valid fields to update.")

    query_set_string = ", ".join(set_clauses)
    
    # Add record_id to the end of update_values for the WHERE clause
    update_values.append(record_id)

    # Construct the full SQL query
    # RETURNING all fields needed for MyAttendanceRecord
    sql_update_query = f"""
        UPDATE attendance
        SET {query_set_string}
        WHERE record_id = ${param_idx} AND deleted_at IS NULL
        RETURNING record_id, job_id, session_date, start_time, end_time, duration,
                  activity_log, photo_proof_url, tutor_id, tutor_name, client_ids,
                  student_names, program_title, status, mentor_rate, 
                  rate_type, submitted_at, deleted_at;
    """ # client_rate is not in RETURNING for MyAttendanceRecord

    try:
        updated_record_db = await conn.fetchrow(sql_update_query, *update_values)

        if not updated_record_db:
            # This could happen if the record was deleted between the initial fetch and the update,
            # or if record_id was invalid (though initial check should catch this).
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attendance record not found during update, or no changes made.")

        record_dict = dict(updated_record_db)
        
        if record_dict.get('mentor_rate') is not None:
            record_dict['mentor_rate'] = float(record_dict['mentor_rate'])
        
        # client_rate is not part of MyAttendanceRecord, so no need to handle it here for the response
            
        return MyAttendanceRecord(**record_dict)

    except asyncpg.exceptions.DataError as e: # Catch errors like invalid date/time format
        print(f"DataError during attendance update: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid data format: {e}")
    except Exception as e:
        print(f"Error updating attendance record {record_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")

# --- Invoice Management Endpoints ---

@app.post("/invoices/generate", response_model=List[Invoice], status_code=status.HTTP_201_CREATED)
async def generate_invoices(
    request_data: InvoiceCreateRequest,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Generates invoices for selected approved attendance records.
    - Superadmin access required.
    - An invoice is created for each unique client found in the selected attendance records.
    - Attendance records are marked with the generated invoice_id.
    """
    user_id_str, user_role = await get_current_user_role(token)
    if user_role != "superadmin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Superadmin access required.")
    
    generating_user_id = int(user_id_str)

    # 1. Fetch selected, approved, and not-yet-invoiced attendance records
    # Ensure client_rate and rate_type are present for calculation
    query_attendance = """
        SELECT record_id, job_id, session_date, duration, client_ids, program_title, 
               client_rate, rate_type, status, invoice_ids, tutor_name
        FROM attendance
        WHERE record_id = ANY($1::int[]) AND status = 'approved' AND session_date <= $2
        -- Optional: Add a condition if you only want to process records not yet part of ANY invoice:
        -- AND (invoice_ids IS NULL OR cardinality(invoice_ids) = 0) 
        -- For now, we'll allow appending to existing invoice_ids arrays.
    """
    attendance_records_db = await conn.fetch(query_attendance, request_data.attendance_record_ids, request_data.cutoff_date)

    if not attendance_records_db:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No eligible (approved, within cutoff date) attendance records found for the given IDs.")
    
    # Group attendance records by client_id
    # An attendance record can have multiple client_ids. This means one attendance record
    # might contribute to multiple invoices if it serves multiple clients.
    # The `invoice_id` on the attendance table (singular) becomes problematic here.
    # For this implementation, we'll assume that if an attendance record is part of ANY invoice,
    # its `invoice_id` will be set. This might need refinement based on business logic
    # (e.g., does `attendance.invoice_id` point to the *first* invoice it's part of, or is this an issue?)
    #
    # Clarification needed: If AR1 has client_ids [C1, C2] and is included in Invoice_C1 and Invoice_C2,
    # what should AR1.invoice_id be?
    # For now, we'll update AR1.invoice_id with the ID of the *first* invoice it's processed into.
    # A more robust solution might involve a linking table or an array `invoice_ids` on attendance.

    client_attendance_map = {} # {client_id: [attendance_record_objects]}
    import sys
    for ar_db in attendance_records_db:
        ar = dict(ar_db)
        print(f"[generate_invoices] Attendance record: {ar}", file=sys.stderr)
        # Nested check for missing data, for clearer diagnostics
        if not ar.get('client_ids'):
            print(f"[generate_invoices] Skipping attendance record {ar['record_id']} due to missing client_ids.", file=sys.stderr)
            continue
        if ar.get('client_rate') is None:
            print(f"[generate_invoices] Skipping attendance record {ar['record_id']} due to missing client_rate.", file=sys.stderr)
            continue
        for client_id_in_ar in ar['client_ids']:
            if client_id_in_ar not in client_attendance_map:
                client_attendance_map[client_id_in_ar] = []
            client_attendance_map[client_id_in_ar].append(ar)

    generated_invoices = []

    async with conn.transaction(): # Use a transaction for atomicity
        for client_id, client_ars in client_attendance_map.items():
            client_info_db = await conn.fetchrow("SELECT client_id, student_name, student_whatsapp, pj_name, email, pj_whatsapp, school, grade FROM clients WHERE client_id = $1 AND deleted_at IS NULL", client_id)
            if not client_info_db:
                print(f"Client ID {client_id} not found or deleted. Skipping invoice generation for this client.")
                continue
            
            client_snapshot = ClientSnapshot(**dict(client_info_db))

            line_items_data = []
            subtotal = Decimal("0.00")

            current_invoice_attendance_ids = [] # Keep track of ARs for THIS invoice

            for ar_data in client_ars:
                import sys
                print(f"[generate_invoices] Processing AR for invoice: record_id={ar_data['record_id']}, duration={ar_data['duration']}, rate_type={ar_data['rate_type']}, client_rate={ar_data['client_rate']}", file=sys.stderr)
                quantity = 1.0
                unit_price = Decimal(str(ar_data['client_rate'] or 0)) # Ensure Decimal

                if ar_data['rate_type'] == "per hour":
                    quantity = parse_duration_to_hours(ar_data['duration'])
                    print(f"[generate_invoices] Calculated quantity (hours) for record_id={ar_data['record_id']}: {quantity}", file=sys.stderr)
                else:
                    print(f"[generate_invoices] Using default quantity=1 for record_id={ar_data['record_id']}", file=sys.stderr)

                total_price = Decimal(str(quantity)) * unit_price
                print(f"[generate_invoices] total_price for record_id={ar_data['record_id']}: {total_price} (quantity={quantity} * unit_price={unit_price})", file=sys.stderr)

                line_items_data.append(InvoiceLineItem(
                    attendance_record_id=ar_data['record_id'],
                    description=f"{ar_data['session_date']} : {ar_data['program_title']} (Tutor: {ar_data['tutor_name']})",
                    quantity=quantity,
                    unit_price=float(unit_price),
                    total_price=float(total_price)
                ))
                subtotal += total_price
                current_invoice_attendance_ids.append(ar_data['record_id'])
            
            if not line_items_data: # No items for this client from the selection
                continue

            next_sequence_value = await conn.fetchval("SELECT nextval('invoice_number_seq')")
            invoice_number = f"INV-{next_sequence_value}"

            total_amount = subtotal # Adjustments are typically added during verification

            # Insert invoice
            insert_invoice_query = """
                INSERT INTO invoices (client_id, invoice_number, issue_date, due_date, status, client_details, 
                                      line_items, subtotal, total_amount, generated_by_user_id, adjustments)
                VALUES ($1, $2, CURRENT_DATE, $3, 'checking', $4, $5, $6, $7, $8, '[]'::jsonb)
                RETURNING invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
                          line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
                          verified_by_user_id, created_at, updated_at, payment_details, notes;
            """
            new_invoice_db = await conn.fetchrow(
                insert_invoice_query,
                client_id, invoice_number, request_data.due_date, json.dumps(client_snapshot.model_dump()), 
                json.dumps([item.model_dump() for item in line_items_data]), 
                subtotal, total_amount, generating_user_id
            )
            
            if new_invoice_db:
                new_invoice_id = new_invoice_db['invoice_id']
                # Update attendance records to append the new_invoice_id to their invoice_ids array
                # Ensure not to add duplicates if this AR is processed multiple times for the same invoice (shouldn't happen with current_invoice_attendance_ids logic)
                if current_invoice_attendance_ids:
                    await conn.execute(
                        """
                        UPDATE attendance
                        SET invoice_ids = array_append(COALESCE(invoice_ids, '{}'::int[]), $1)
                        WHERE record_id = ANY($2::int[]) AND NOT ($1 = ANY(COALESCE(invoice_ids, '{}'::int[])))
                        """,
                        new_invoice_id, list(set(current_invoice_attendance_ids)) # Use set to ensure unique record_ids
                    )

                # Convert Decimal to float for Pydantic model
                invoice_dict = dict(new_invoice_db)
                invoice_dict['subtotal'] = float(invoice_dict['subtotal'])
                invoice_dict['total_amount'] = float(invoice_dict['total_amount'])
                invoice_dict['client_details'] = json.loads(invoice_dict['client_details']) # Parse JSON string
                invoice_dict['line_items'] = json.loads(invoice_dict['line_items']) # Parse JSON string
                invoice_dict['adjustments'] = json.loads(invoice_dict['adjustments'])

                generated_invoices.append(Invoice(**invoice_dict))

    return generated_invoices


@app.get("/invoices", response_model=List[Invoice])
async def list_invoices(
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection),
    client_id: Optional[int] = None, # Query param to filter by client
    status: Optional[str] = None # Query param to filter by status
):
    """Lists all invoices, optionally filtered by client_id and/or status. Admin/Superadmin access."""
    await verify_admin_access(token) # Ensures admin or superadmin

    query_conditions = []
    query_params = []
    param_idx = 1

    if client_id:
        query_conditions.append(f"client_id = ${param_idx}")
        query_params.append(client_id)
        param_idx += 1
    
    if status:
        query_conditions.append(f"status = ${param_idx}")
        query_params.append(status)
        param_idx += 1

    where_clause = f"WHERE {' AND '.join(query_conditions)}" if query_conditions else ""
    
    query = f"""
        SELECT invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
               line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
               verified_by_user_id, created_at, updated_at, payment_details, notes
        FROM invoices
        {where_clause}
        ORDER BY issue_date DESC, invoice_id DESC;
    """
    invoices_db = await conn.fetch(query, *query_params)
    
    result_invoices = []
    for inv_db in invoices_db:
        inv_dict = dict(inv_db)
        inv_dict['subtotal'] = float(inv_dict['subtotal'])
        inv_dict['total_amount'] = float(inv_dict['total_amount'])
        inv_dict['client_details'] = json.loads(inv_dict['client_details'])
        inv_dict['line_items'] = json.loads(inv_dict['line_items'])
        inv_dict['adjustments'] = json.loads(inv_dict['adjustments'])
        if inv_dict.get('payment_details'):
            inv_dict['payment_details'] = json.loads(inv_dict['payment_details'])
        result_invoices.append(Invoice(**inv_dict))
    return result_invoices


@app.get("/invoices/{invoice_id}", response_model=Invoice)
async def get_invoice_details(invoice_id: int, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Gets details of a specific invoice. Admin/Superadmin access."""
    await verify_admin_access(token)
    
    inv_db = await conn.fetchrow(
        """
        SELECT invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
               line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
               verified_by_user_id, created_at, updated_at, payment_details, notes
        FROM invoices WHERE invoice_id = $1
        """, invoice_id
    )
    if not inv_db:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Invoice not found.")

    inv_dict = dict(inv_db)
    inv_dict['subtotal'] = float(inv_dict['subtotal'])
    inv_dict['total_amount'] = float(inv_dict['total_amount'])
    inv_dict['client_details'] = json.loads(inv_dict['client_details'])
    inv_dict['line_items'] = json.loads(inv_dict['line_items'])
    inv_dict['adjustments'] = json.loads(inv_dict['adjustments'])
    if inv_dict.get('payment_details'):
            inv_dict['payment_details'] = json.loads(inv_dict['payment_details'])
    return Invoice(**inv_dict)


@app.put("/invoices/{invoice_id}/verify", response_model=Invoice)
async def verify_invoice(
    invoice_id: int, 
    update_data: InvoiceAdminUpdate,
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    user_id_str, user_role = await get_current_user_role(token)
    if user_role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required.")
    
    verifying_user_id = int(user_id_str)

    async with conn.transaction():
        invoice_db_record = await conn.fetchrow("SELECT invoice_id, status, subtotal, adjustments FROM invoices WHERE invoice_id = $1 FOR UPDATE", invoice_id)
        if not invoice_db_record:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Invoice not found.")
        if invoice_db_record['status'] != 'checking':
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invoice cannot be verified. Current status: {invoice_db_record['status']}.")

        current_subtotal = Decimal(str(invoice_db_record['subtotal']))
        current_adjustments_json_from_db = invoice_db_record['adjustments']
        current_adjustments_pydantic = [AdjustmentItem(**adj) for adj in json.loads(current_adjustments_json_from_db)] if current_adjustments_json_from_db else []

        set_clauses = []
        update_values = [] # List to hold values for parameterization
        param_idx = 1 # Start parameter index from 1

        # Status and verified_by_user_id
        set_clauses.append(f"status = ${param_idx}")
        update_values.append('verified')
        param_idx += 1
        set_clauses.append(f"verified_by_user_id = ${param_idx}")
        update_values.append(verifying_user_id)
        param_idx += 1
        
        final_adjustments_pydantic: List[AdjustmentItem]
        if update_data.adjustments is not None:
            final_adjustments_pydantic = update_data.adjustments
        else:
            final_adjustments_pydantic = current_adjustments_pydantic
        
        final_adjustments_json_for_db = json.dumps([adj.model_dump() for adj in final_adjustments_pydantic])
        set_clauses.append(f"adjustments = ${param_idx}")
        update_values.append(final_adjustments_json_for_db) # asyncpg handles JSONB correctly
        param_idx += 1

        total_adjustment_amount = sum(Decimal(str(adj.amount)) for adj in final_adjustments_pydantic)
        new_total_amount = current_subtotal + total_adjustment_amount
        set_clauses.append(f"total_amount = ${param_idx}")
        update_values.append(new_total_amount) # asyncpg handles Decimal correctly
        param_idx += 1

        if update_data.due_date is not None:
            set_clauses.append(f"due_date = ${param_idx}")
            update_values.append(update_data.due_date)
            param_idx += 1
        if update_data.notes is not None:
            set_clauses.append(f"notes = ${param_idx}")
            update_values.append(update_data.notes)
            param_idx += 1
        
        # The WHERE clause parameter
        update_values.append(invoice_id)

        update_query = f"""
            UPDATE invoices SET {', '.join(set_clauses)}
            WHERE invoice_id = ${param_idx} -- This is the placeholder for invoice_id
            RETURNING invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
                      line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
                      verified_by_user_id, created_at, updated_at, payment_details, notes;
        """
        # Pass all values in update_values list
        updated_inv_db = await conn.fetchrow(update_query, *update_values)


    if not updated_inv_db:
         raise HTTPException(status_code=500, detail="Failed to update invoice.")

    inv_dict = dict(updated_inv_db)
    inv_dict['subtotal'] = float(inv_dict['subtotal'])
    inv_dict['total_amount'] = float(inv_dict['total_amount'])
    inv_dict['client_details'] = json.loads(inv_dict['client_details'])
    inv_dict['line_items'] = json.loads(inv_dict['line_items'])
    inv_dict['adjustments'] = json.loads(inv_dict['adjustments'])
    if inv_dict.get('payment_details'):
            inv_dict['payment_details'] = json.loads(inv_dict['payment_details'])
    return Invoice(**inv_dict)


@app.put("/invoices/{invoice_id}/status", response_model=Invoice)
async def update_invoice_status(
    invoice_id: int, 
    status_update: InvoiceStatusUpdateRequest, 
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Admin updates invoice status (verified -> sent, sent -> paid, or -> cancelled).
    If cancelling, all related invoices in the 'web' are checked. If none are 'sent' or 'paid',
    all related invoices are cancelled and linked attendance records are unlinked.
    """
    user_id_str, user_role = await get_current_user_role(token)
    if user_role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required.")

    async with conn.transaction():
        invoice = await conn.fetchrow("SELECT invoice_id, status FROM invoices WHERE invoice_id = $1 FOR UPDATE", invoice_id)
        if not invoice:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Invoice not found.")

        current_status = invoice['status']
        new_status = status_update.status

        # Basic status transition validation
        allowed_transitions = {
            'checking': ['verified', 'cancelled'], # Verification endpoint handles checking -> verified
            'verified': ['sent', 'cancelled'],
            'sent': ['paid', 'cancelled'],
            'paid': [],
            'cancelled': []
        }
        if new_status not in allowed_transitions.get(current_status, []):
             # This check is important. If an invoice is 'paid', it cannot be transitioned to 'cancelled' here.
             raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Cannot change status from '{current_status}' to '{new_status}'.")

        updated_inv_db = None

        if new_status == 'cancelled':
            # --- Start of new complex cancellation logic ---
            target_invoice_id = invoice_id
            
            all_related_invoice_ids = {target_invoice_id}
            all_related_attendance_ids = set()
            
            # Queues for BFS-like traversal
            invoices_to_process_for_attendance = {target_invoice_id}
            attendance_to_process_for_invoices = set() # Stores att_ids to find their invoices
            
            # Sets to keep track of processed items to avoid redundant DB calls and loops
            processed_invoices_for_attendance_step = set()
            processed_attendance_for_invoices_step = set()

            while invoices_to_process_for_attendance or attendance_to_process_for_invoices:
                
                # Step 1: Process invoices to find related attendance records
                # Take a snapshot of current invoices to process to avoid issues with modifying set while iterating
                current_batch_invoices = list(invoices_to_process_for_attendance)
                invoices_to_process_for_attendance.clear()

                for inv_id_to_scan in current_batch_invoices:
                    if inv_id_to_scan in processed_invoices_for_attendance_step:
                        continue
                    
                    inv_line_items_json = await conn.fetchval("SELECT line_items FROM invoices WHERE invoice_id = $1", inv_id_to_scan)
                    if inv_line_items_json:
                        inv_line_items = json.loads(inv_line_items_json)
                        for item in inv_line_items:
                            att_id = item['attendance_record_id']
                            all_related_attendance_ids.add(att_id)
                            if att_id not in processed_attendance_for_invoices_step:
                                 attendance_to_process_for_invoices.add(att_id) # Add to next queue
                    processed_invoices_for_attendance_step.add(inv_id_to_scan)

                # Step 2: Process attendance records to find related invoices
                current_batch_attendance = list(attendance_to_process_for_invoices)
                attendance_to_process_for_invoices.clear()

                for att_id_to_scan in current_batch_attendance:
                    if att_id_to_scan in processed_attendance_for_invoices_step:
                        continue

                    att_invoice_ids_list = await conn.fetchval("SELECT invoice_ids FROM attendance WHERE record_id = $1", att_id_to_scan)
                    if att_invoice_ids_list: # This is a list of integers from DB (array type)
                        for inv_id_from_att in att_invoice_ids_list:
                            all_related_invoice_ids.add(inv_id_from_att)
                            if inv_id_from_att not in processed_invoices_for_attendance_step:
                                 invoices_to_process_for_attendance.add(inv_id_from_att) # Add to next queue
                    processed_attendance_for_invoices_step.add(att_id_to_scan)
            
            # Check statuses of all related invoices
            if all_related_invoice_ids:
                related_inv_ids_array = list(all_related_invoice_ids)
                query_statuses = "SELECT invoice_id, status FROM invoices WHERE invoice_id = ANY($1::int[])"
                statuses_records = await conn.fetch(query_statuses, related_inv_ids_array)
                
                for r_inv_status_record in statuses_records:
                    r_inv_status = r_inv_status_record['status']
                    r_inv_id = r_inv_status_record['invoice_id']
                    if r_inv_status in ['sent', 'paid']:
                        raise HTTPException(
                            status_code=status.HTTP_409_CONFLICT,
                            detail=f"Cannot cancel invoice {target_invoice_id}. A related invoice (ID: {r_inv_id}) is already '{r_inv_status}'. All related invoices must be in 'checking' or 'verified' state to proceed with cancellation."
                        )
            
            # If all checks pass, proceed with cancellation
            cancellation_notes = status_update.notes if status_update.notes else f"Cancelled as part of cancelling invoice {target_invoice_id} and its related web."
            
            if all_related_invoice_ids:
                await conn.execute(
                    "UPDATE invoices SET status = 'cancelled', notes = $2 WHERE invoice_id = ANY($1::int[])",
                    list(all_related_invoice_ids), cancellation_notes
                )
            
            if all_related_attendance_ids:
                await conn.execute(
                    "UPDATE attendance SET invoice_ids = NULL WHERE record_id = ANY($1::int[])",
                    list(all_related_attendance_ids)
                )
            
            updated_inv_db = await conn.fetchrow(
                """
                SELECT invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
                       line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
                       verified_by_user_id, created_at, updated_at, payment_details, notes
                FROM invoices WHERE invoice_id = $1
                """, target_invoice_id
            )
            # --- End of new complex cancellation logic ---
        else:
            # --- Parameterized existing logic for other status updates (sent, paid) ---
            set_clauses = []
            update_params = []
            param_idx = 1

            set_clauses.append(f"status = ${param_idx}")
            update_params.append(new_status)
            param_idx += 1

            if new_status == 'paid':
                if not status_update.payment_details:
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Payment details are required when marking invoice as paid.")
                set_clauses.append(f"payment_details = ${param_idx}::jsonb")
                update_params.append(json.dumps(status_update.payment_details)) # Ensure json string for jsonb
                param_idx += 1
            
            if status_update.notes:
                 set_clauses.append(f"notes = ${param_idx}")
                 update_params.append(status_update.notes)
                 param_idx += 1
            
            update_params.append(invoice_id) # For WHERE invoice_id = $N

            update_query_str = f"""
                UPDATE invoices SET {', '.join(set_clauses)}
                WHERE invoice_id = ${param_idx}
                RETURNING invoice_id, client_id, invoice_number, issue_date, due_date, status, client_details, 
                          line_items, subtotal, adjustments, total_amount, generated_by_user_id, 
                          verified_by_user_id, created_at, updated_at, payment_details, notes;
            """
            updated_inv_db = await conn.fetchrow(update_query_str, *update_params)
            # --- End of parameterized existing logic ---

    if not updated_inv_db:
         # This case might be hit if the target invoice was somehow deleted mid-transaction by another process,
         # or if the cancellation logic didn't refetch it (but it does now).
         raise HTTPException(status_code=500, detail="Failed to update invoice status or retrieve updated invoice.")

    inv_dict = dict(updated_inv_db)
    inv_dict['subtotal'] = float(inv_dict['subtotal'])
    inv_dict['total_amount'] = float(inv_dict['total_amount'])
    inv_dict['client_details'] = json.loads(inv_dict['client_details'])
    inv_dict['line_items'] = json.loads(inv_dict['line_items'])
    inv_dict['adjustments'] = json.loads(inv_dict['adjustments'])
    if inv_dict.get('payment_details') and isinstance(inv_dict['payment_details'], str): # Ensure parsing if it's a string
            inv_dict['payment_details'] = json.loads(inv_dict['payment_details'])
    elif inv_dict.get('payment_details') is None: # Ensure it's None if DB returns NULL
            inv_dict['payment_details'] = None

    return Invoice(**inv_dict)


# --- Payslip Management Endpoints ---

@app.post("/payslips/generate", response_model=List[Payslip], status_code=status.HTTP_201_CREATED)
async def generate_payslips(
    request_data: PayslipCreateRequest,
    token: str,
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Generates payslips for tutors based on invoiced, approved, and not-yet-payslipped attendance records
    within the specified pay period.
    - Admin/Superadmin access required.
    """
    user_id_str, user_role = await get_current_user_role(token)
    if user_role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin/Superadmin access required.")
    
    generating_user_id = int(user_id_str)

    # 1. Fetch eligible attendance records
    query_attendance = """
        SELECT record_id, job_id, session_date, duration, tutor_id, tutor_name, program_title, mentor_rate, rate_type, status, invoice_ids, student_names
        FROM attendance
        WHERE status = 'approved' 
          AND (invoice_ids IS NOT NULL AND cardinality(invoice_ids) > 0) -- Check if array is not null and not empty
          AND payslip_id IS NULL
          AND session_date >= $1 
          AND session_date <= $2
    """
    attendance_records_db = await conn.fetch(query_attendance, request_data.pay_period_start_date, request_data.pay_period_end_date)

    if not attendance_records_db:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No eligible attendance records found for payslip generation in the given period.")

    tutor_attendance_map = {} # {tutor_id: [attendance_record_objects]}
    for ar_db in attendance_records_db:
        ar = dict(ar_db)
        if not ar.get('tutor_id') or ar.get('mentor_rate') is None: # mentor_rate can be 0
            print(f"Skipping attendance record {ar['record_id']} due to missing tutor_id or mentor_rate.")
            continue
        
        tutor_id = ar['tutor_id']
        if tutor_id not in tutor_attendance_map:
            tutor_attendance_map[tutor_id] = []
        tutor_attendance_map[tutor_id].append(ar)

    generated_payslips = []
    
    async with conn.transaction():
        for tutor_id, tutor_ars in tutor_attendance_map.items():
            tutor_info_db = await conn.fetchrow("SELECT user_id, name, email, whatsapp FROM users WHERE user_id = $1 AND deleted_at IS NULL", tutor_id)
            if not tutor_info_db:
                print(f"Tutor ID {tutor_id} not found or deleted. Skipping payslip generation for this tutor.")
                continue
            
            tutor_snapshot = TutorSnapshot(**dict(tutor_info_db))

            earnings_items_data = []
            subtotal_earnings = Decimal("0.00")
            payslip_attendance_ids = []

            for ar_data in tutor_ars:
                quantity = 1.0
                rate = Decimal(str(ar_data['mentor_rate'] or 0))

                if ar_data['rate_type'] == "per hour": # Assuming rate_type applies to mentor_rate as well
                    quantity = parse_duration_to_hours(ar_data['duration'])
                
                total_earned = Decimal(str(quantity)) * rate
                
                earnings_items_data.append(PayslipEarningItem(
                    attendance_record_id=ar_data['record_id'],
                    description=f"{ar_data['program_title']} on {ar_data['session_date']} {ar_data['student_names']}",
                    quantity=quantity,
                    rate=float(rate),
                    total_earned=float(total_earned)
                ))
                subtotal_earnings += total_earned
                payslip_attendance_ids.append(ar_data['record_id'])
            
            if not earnings_items_data:
                continue

            payslip_number = await generate_unique_number(conn, "PAY", "payslips", "payslip_number")
            total_payout = subtotal_earnings # Adjustments typically added during verification

            insert_payslip_query = """
                INSERT INTO payslips (user_id, payslip_number, pay_period_start_date, pay_period_end_date, 
                                      issue_date, status, tutor_details, earnings_items, subtotal_earnings, 
                                      total_payout, generated_by_user_id, adjustments)
                VALUES ($1, $2, $3, $4, CURRENT_DATE, 'checking', $5, $6, $7, $8, $9, '[]'::jsonb)
                RETURNING payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
                          status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
                          generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date, notes;
            """
            new_payslip_db = await conn.fetchrow(
                insert_payslip_query,
                tutor_id, payslip_number, request_data.pay_period_start_date, request_data.pay_period_end_date,
                json.dumps(tutor_snapshot.model_dump()), 
                json.dumps([item.model_dump() for item in earnings_items_data]), 
                subtotal_earnings, total_payout, generating_user_id
            )
            
            if new_payslip_db:
                new_payslip_id = new_payslip_db['payslip_id']
                # Mark attendance records with this payslip_id
                await conn.execute(
                    "UPDATE attendance SET payslip_id = $1 WHERE record_id = ANY($2::int[])",
                    new_payslip_id, payslip_attendance_ids
                )
                
                payslip_dict = dict(new_payslip_db)
                payslip_dict['subtotal_earnings'] = float(payslip_dict['subtotal_earnings'])
                payslip_dict['total_payout'] = float(payslip_dict['total_payout'])
                payslip_dict['tutor_details'] = json.loads(payslip_dict['tutor_details'])
                payslip_dict['earnings_items'] = json.loads(payslip_dict['earnings_items'])
                payslip_dict['adjustments'] = json.loads(payslip_dict['adjustments'])
                generated_payslips.append(Payslip(**payslip_dict))

    return generated_payslips


@app.get("/payslips", response_model=List[Payslip])
async def list_payslips(
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection),
    tutor_id: Optional[int] = None, 
    status: Optional[str] = None
):
    """Lists all payslips, optionally filtered by tutor_id and/or status. Admin/Superadmin access."""
    await verify_admin_access(token)

    query_conditions = []
    query_params = []
    param_idx = 1

    if tutor_id:
        query_conditions.append(f"user_id = ${param_idx}") # user_id is tutor_id in payslips table
        query_params.append(tutor_id)
        param_idx += 1
    
    if status:
        query_conditions.append(f"status = ${param_idx}")
        query_params.append(status)
        param_idx += 1

    where_clause = f"WHERE {' AND '.join(query_conditions)}" if query_conditions else ""
    
    query = f"""
        SELECT payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
               status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
               generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date, notes
        FROM payslips
        {where_clause}
        ORDER BY issue_date DESC, payslip_id DESC;
    """
    payslips_db = await conn.fetch(query, *query_params)
    
    result_payslips = []
    for ps_db in payslips_db:
        ps_dict = dict(ps_db)
        ps_dict['subtotal_earnings'] = float(ps_dict['subtotal_earnings'])
        ps_dict['total_payout'] = float(ps_dict['total_payout'])
        ps_dict['tutor_details'] = json.loads(ps_dict['tutor_details'])
        ps_dict['earnings_items'] = json.loads(ps_dict['earnings_items'])
        ps_dict['adjustments'] = json.loads(ps_dict['adjustments'])
        result_payslips.append(Payslip(**ps_dict))
    return result_payslips


@app.get("/payslips/me", response_model=List[Payslip])
async def get_my_payslips(token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Retrieves payslips for the currently authenticated tutor."""
    user_id_str, _ = await get_current_user_role(token)
    tutor_user_id = int(user_id_str)

    query = """
        SELECT payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
               status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
               generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date, notes
        FROM payslips
        WHERE user_id = $1 AND status != 'checking' -- Tutors usually see verified or paid slips
        ORDER BY issue_date DESC, payslip_id DESC;
    """ # Or filter by status in ('verified', 'paid')
    payslips_db = await conn.fetch(query, tutor_user_id)
    
    result_payslips = []
    for ps_db in payslips_db:
        ps_dict = dict(ps_db)
        ps_dict['subtotal_earnings'] = float(ps_dict['subtotal_earnings'])
        ps_dict['total_payout'] = float(ps_dict['total_payout'])
        ps_dict['tutor_details'] = json.loads(ps_dict['tutor_details'])
        ps_dict['earnings_items'] = json.loads(ps_dict['earnings_items'])
        ps_dict['adjustments'] = json.loads(ps_dict['adjustments'])
        result_payslips.append(Payslip(**ps_dict))
    return result_payslips


@app.get("/payslips/{payslip_id}", response_model=Payslip)
async def get_payslip_details(payslip_id: int, token: str, conn: asyncpg.Connection = Depends(get_db_connection)):
    """Gets details of a specific payslip. Admin/Superadmin access."""
    await verify_admin_access(token)
    
    ps_db = await conn.fetchrow(
        """
        SELECT payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
               status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
               generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date, notes
        FROM payslips WHERE payslip_id = $1
        """, payslip_id
    )
    if not ps_db:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Payslip not found.")

    ps_dict = dict(ps_db)
    ps_dict['subtotal_earnings'] = float(ps_dict['subtotal_earnings'])
    ps_dict['total_payout'] = float(ps_dict['total_payout'])
    ps_dict['tutor_details'] = json.loads(ps_dict['tutor_details'])
    ps_dict['earnings_items'] = json.loads(ps_dict['earnings_items'])
    ps_dict['adjustments'] = json.loads(ps_dict['adjustments'])
    return Payslip(**ps_dict)


@app.put("/payslips/{payslip_id}/verify", response_model=Payslip)
async def verify_payslip(
    payslip_id: int, 
    update_data: PayslipAdminUpdate,
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    user_id_str, user_role = await get_current_user_role(token)
    if user_role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required.")
    
    verifying_user_id = int(user_id_str)

    async with conn.transaction():
        payslip_db_record = await conn.fetchrow("SELECT payslip_id, status, subtotal_earnings, adjustments FROM payslips WHERE payslip_id = $1 FOR UPDATE", payslip_id)
        if not payslip_db_record:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Payslip not found.")
        if payslip_db_record['status'] != 'checking':
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Payslip cannot be verified. Current status: {payslip_db_record['status']}.")

        current_subtotal = Decimal(str(payslip_db_record['subtotal_earnings']))
        current_adjustments_json_from_db = payslip_db_record['adjustments']
        current_adjustments_pydantic = [AdjustmentItem(**adj) for adj in json.loads(current_adjustments_json_from_db)] if current_adjustments_json_from_db else []
        
        set_clauses = []
        update_values = [] # List to hold values for parameterization
        param_idx = 1 # Start parameter index from 1

        # Status and verified_by_user_id
        set_clauses.append(f"status = ${param_idx}")
        update_values.append('verified')
        param_idx += 1
        set_clauses.append(f"verified_by_user_id = ${param_idx}")
        update_values.append(verifying_user_id)
        param_idx += 1
        
        final_adjustments_pydantic: List[AdjustmentItem]
        if update_data.adjustments is not None:
            final_adjustments_pydantic = update_data.adjustments
        else:
            final_adjustments_pydantic = current_adjustments_pydantic
        
        final_adjustments_json_for_db = json.dumps([adj.model_dump() for adj in final_adjustments_pydantic])
        set_clauses.append(f"adjustments = ${param_idx}")
        update_values.append(final_adjustments_json_for_db)
        param_idx += 1

        total_adjustment_amount = sum(Decimal(str(adj.amount)) for adj in final_adjustments_pydantic)
        new_total_payout = current_subtotal + total_adjustment_amount
        set_clauses.append(f"total_payout = ${param_idx}")
        update_values.append(new_total_payout)
        param_idx += 1

        if update_data.notes is not None:
            set_clauses.append(f"notes = ${param_idx}")
            update_values.append(update_data.notes)
            param_idx += 1
        
        # The WHERE clause parameter
        update_values.append(payslip_id)

        update_query = f"""
            UPDATE payslips SET {', '.join(set_clauses)}
            WHERE payslip_id = ${param_idx} -- This is the placeholder for payslip_id
            RETURNING payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
                      status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
                      generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date,
                      notes;
        """
        updated_ps_db = await conn.fetchrow(update_query, *update_values)

    if not updated_ps_db:
         raise HTTPException(status_code=500, detail="Failed to update payslip.")

    ps_dict = dict(updated_ps_db)
    ps_dict['subtotal_earnings'] = float(ps_dict['subtotal_earnings'])
    ps_dict['total_payout'] = float(ps_dict['total_payout'])
    ps_dict['tutor_details'] = json.loads(ps_dict['tutor_details'])
    ps_dict['earnings_items'] = json.loads(ps_dict['earnings_items'])
    ps_dict['adjustments'] = json.loads(ps_dict['adjustments'])
    return Payslip(**ps_dict)


@app.put("/payslips/{payslip_id}/status", response_model=Payslip)
async def update_payslip_status(
    payslip_id: int, 
    status_update: PayslipStatusUpdateRequest, 
    token: str, 
    conn: asyncpg.Connection = Depends(get_db_connection)
):
    """
    Admin updates payslip status (verified -> paid, or -> cancelled).
    """
    user_id_str, user_role = await get_current_user_role(token)
    if user_role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required.")

    async with conn.transaction():
        payslip = await conn.fetchrow("SELECT payslip_id, status FROM payslips WHERE payslip_id = $1 FOR UPDATE", payslip_id)
        if not payslip:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Payslip not found.")

        current_status = payslip['status']
        new_status = status_update.status

        allowed_transitions = {
            'checking': ['verified', 'cancelled'], # Verification endpoint handles checking -> verified
            'verified': ['paid', 'cancelled'],
            'paid': [],
            'cancelled': []
        }
        if new_status not in allowed_transitions.get(current_status, []):
             if not (current_status == 'checking' and new_status == 'verified' and status_update.status == 'verified'):
                 raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Cannot change status from '{current_status}' to '{new_status}'.")

        set_clauses = [f"status = '{new_status}'"]
        if new_status == 'paid':
            if not status_update.payment_date: # payment_date is optional in model, but required for 'paid' status
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Payment date is required when marking payslip as paid.")
            set_clauses.append(f"payment_date = '{status_update.payment_date}'")
        
        if status_update.notes:
             set_clauses.append(f"notes = '{status_update.notes}'")

        update_query = f"""
            UPDATE payslips SET {', '.join(set_clauses)}
            WHERE payslip_id = $1
            RETURNING payslip_id, user_id, payslip_number, pay_period_start_date, pay_period_end_date, issue_date, 
                      status, tutor_details, earnings_items, subtotal_earnings, adjustments, total_payout, 
                      generated_by_user_id, verified_by_user_id, created_at, updated_at, payment_date, notes;
        """
        updated_ps_db = await conn.fetchrow(update_query, payslip_id)

    if not updated_ps_db:
         raise HTTPException(status_code=500, detail="Failed to update payslip status.")

    # === Start of ADDED logic for cancellation ===
    if new_status == 'cancelled':
        # Reset payslip_id on associated attendance records
        await conn.execute(
            "UPDATE attendance SET payslip_id = NULL WHERE payslip_id = $1",
            payslip_id
        )
        print(f"Payslip {payslip_id} cancelled. Associated attendance records have been unlinked.")
    # === End of ADDED logic for cancellation ===

    ps_dict = dict(updated_ps_db)
    ps_dict['subtotal_earnings'] = float(ps_dict['subtotal_earnings'])
    ps_dict['total_payout'] = float(ps_dict['total_payout'])
    ps_dict['tutor_details'] = json.loads(ps_dict['tutor_details'])
    ps_dict['earnings_items'] = json.loads(ps_dict['earnings_items'])
    ps_dict['adjustments'] = json.loads(ps_dict['adjustments'])
    return Payslip(**ps_dict)



# --- Main execution (for running with uvicorn) ---
# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)