from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ARRAY, JSON, Date, Time, DECIMAL, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    whatsapp = Column(String)
    expertise = Column(String)
    hashed_password = Column(String, nullable=False)
    role = Column(String, default="tutor", nullable=False)  # tutor, admin, superadmin
    created_at = Column(DateTime, default=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)

class Client(Base):
    __tablename__ = "clients"
    
    client_id = Column(Integer, primary_key=True, autoincrement=True)
    student_name = Column(String, nullable=False)
    student_whatsapp = Column(String)
    pj_name = Column(String)  # Parent/Guardian name
    email = Column(String)
    pj_whatsapp = Column(String)  # Parent/Guardian whatsapp
    school = Column(String)
    grade = Column(String)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)

class TutoringJob(Base):
    __tablename__ = "tutoring_jobs"
    
    job_id = Column(Integer, primary_key=True, autoincrement=True)
    program_title = Column(String, nullable=False)
    mentor_ids = Column(ARRAY(Integer), nullable=False)  # Array of user IDs
    client_ids = Column(ARRAY(Integer), nullable=False)  # Array of client IDs
    client_rate = Column(DECIMAL(10, 2), default=0.0, nullable=False)
    mentor_rate = Column(DECIMAL(10, 2), default=0.0, nullable=False)
    rate_type = Column(String)  # "per hour" or "per session/program"
    created_at = Column(DateTime, default=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)

class Attendance(Base):
    __tablename__ = "attendance"
    
    record_id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, nullable=False)
    session_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    duration = Column(String)  # e.g., "2 hours", "90 minutes"
    activity_log = Column(Text)
    photo_proof_url = Column(String)
    tutor_id = Column(Integer, nullable=False)
    tutor_name = Column(String, nullable=False)
    client_ids = Column(ARRAY(Integer), nullable=False)
    student_names = Column(ARRAY(String), nullable=False)
    program_title = Column(String, nullable=False)
    status = Column(String, default="pending", nullable=False)  # pending, approved, rejected
    mentor_rate = Column(DECIMAL(10, 2))
    client_rate = Column(DECIMAL(10, 2))
    rate_type = Column(String)
    submitted_at = Column(DateTime, default=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    invoice_ids = Column(ARRAY(Integer))  # Array of invoice IDs
    payslip_id = Column(Integer)

class Invoice(Base):
    __tablename__ = "invoices"
    
    invoice_id = Column(Integer, primary_key=True, autoincrement=True)
    client_id = Column(Integer, nullable=False)
    invoice_number = Column(String, unique=True, nullable=False)
    issue_date = Column(Date, nullable=False)
    due_date = Column(Date)
    status = Column(String, default="checking", nullable=False)  # checking, verified, sent, paid, cancelled
    client_details = Column(JSON, nullable=False)  # Snapshot of client data
    line_items = Column(JSON, nullable=False)  # Array of line items
    subtotal = Column(DECIMAL(10, 2), nullable=False)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    generated_by_user_id = Column(Integer, nullable=False)
    verified_by_user_id = Column(Integer)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    payment_details = Column(JSON)  # Payment information when paid
    notes = Column(Text)
    adjustments = Column(JSON, default='[]')  # Array of adjustment items

class Payslip(Base):
    __tablename__ = "payslips"
    
    payslip_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)  # Tutor's user_id
    payslip_number = Column(String, unique=True, nullable=False)
    pay_period_start_date = Column(Date, nullable=False)
    pay_period_end_date = Column(Date, nullable=False)
    issue_date = Column(Date, nullable=False)
    status = Column(String, default="checking", nullable=False)  # checking, verified, paid, cancelled
    tutor_details = Column(JSON, nullable=False)  # Snapshot of tutor data
    earnings_items = Column(JSON, nullable=False)  # Array of earning items
    subtotal_earnings = Column(DECIMAL(10, 2), nullable=False)
    total_payout = Column(DECIMAL(10, 2), nullable=False)
    generated_by_user_id = Column(Integer, nullable=False)
    verified_by_user_id = Column(Integer)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    payment_date = Column(Date)
    notes = Column(Text)
    adjustments = Column(JSON, default='[]')  # Array of adjustment items
